CMake Warning in C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 193 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


