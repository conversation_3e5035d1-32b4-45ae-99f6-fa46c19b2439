{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-56:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57c3c2f9dced1e219da46eae741c7de7\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,13275", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,13350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a4306a6f482b9baf272025916cdd1d72\\transformed\\play-services-base-18.5.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4806,4913,5086,5216,5325,5472,5601,5714,5968,6130,6239,6412,6544,6697,6858,6923,6989", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "4908,5081,5211,5320,5467,5596,5709,5812,6125,6234,6407,6539,6692,6853,6918,6984,7066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2656d85fa34ff57ee93e0a7799c35d76\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3268,3345,3424,3505,3604,4426,4534,4646,7176,7232,7296,7777,7995,8121,8206,8269,8331,8389,8453,8514,8568,8682,8740,8800,8854,8924,9268,9349,9439,9538,9635,9714,9849,9925,10002,10131,10215,10297,10352,10407,10473,10542,10619,10690,10769,10837,10913,10983,11048,11150,11245,11318,11412,11505,11579,11648,11742,11798,11881,11948,12032,12120,12182,12246,12309,12376,12473,12579,12670,12772,12831,12890,13355,13440,13516", "endLines": "5,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "308,3340,3419,3500,3599,3688,4529,4641,4724,7227,7291,7383,7841,8049,8201,8264,8326,8384,8448,8509,8563,8677,8735,8795,8849,8919,9046,9344,9434,9533,9630,9709,9844,9920,9997,10126,10210,10292,10347,10402,10468,10537,10614,10685,10764,10832,10908,10978,11043,11145,11240,11313,11407,11500,11574,11643,11737,11793,11876,11943,12027,12115,12177,12241,12304,12371,12468,12574,12665,12767,12826,12885,12962,13435,13511,13584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1f6d4087117984f6265a436ffccadbd8\\transformed\\react-android-0.79.1-debug\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,203,274,343,423,490,557,631,707,790,869,937,1015,1098,1172,1256,1344,1419,1490,1561,1647,1716,1790,1859", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,198,269,338,418,485,552,626,702,785,864,932,1010,1093,1167,1251,1339,1414,1485,1556,1642,1711,1785,1854,1927"}, "to": {"startLines": "33,51,77,79,80,82,96,97,98,145,146,147,148,153,154,155,156,157,158,159,160,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,4729,7706,7846,7915,8054,9051,9118,9192,12967,13050,13129,13197,13589,13672,13746,13830,13918,13993,14064,14135,14322,14391,14465,14534", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "3041,4801,7772,7910,7990,8116,9113,9187,9263,13045,13124,13192,13270,13667,13741,13825,13913,13988,14059,14130,14216,14386,14460,14529,14602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a04f17c587cd6ce1f2fc9c2917a06c1d\\transformed\\credentials-1.2.0-rc01\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,111", "endOffsets": "160,272"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3046,3156", "endColumns": "109,111", "endOffsets": "3151,3263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51876ee9437d313540039b485fb1b11c\\transformed\\play-services-basement-18.4.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5817", "endColumns": "150", "endOffsets": "5963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e40193e98f0e5a09a490d3ba62d94d70\\transformed\\browser-1.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7071,7388,7489,7603", "endColumns": "104,100,113,102", "endOffsets": "7171,7484,7598,7701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f320f406cab317ad471420607f539f77\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "41,42,43,44,45,46,47,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3693,3791,3893,3996,4097,4199,4297,14221", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3786,3888,3991,4092,4194,4292,4421,14317"}}]}]}