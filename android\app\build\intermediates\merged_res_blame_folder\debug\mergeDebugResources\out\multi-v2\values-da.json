{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-56:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a04f17c587cd6ce1f2fc9c2917a06c1d\\transformed\\credentials-1.2.0-rc01\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3050,3165", "endColumns": "114,118", "endOffsets": "3160,3279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e40193e98f0e5a09a490d3ba62d94d70\\transformed\\browser-1.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7032,7353,7452,7559", "endColumns": "111,98,106,96", "endOffsets": "7139,7447,7554,7651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a4306a6f482b9baf272025916cdd1d72\\transformed\\play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4830,4936,5096,5223,5332,5475,5600,5720,5952,6108,6214,6376,6503,6648,6826,6892,6954", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4931,5091,5218,5327,5470,5595,5715,5820,6103,6209,6371,6498,6643,6821,6887,6949,7027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f320f406cab317ad471420607f539f77\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "41,42,43,44,45,46,47,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3693,3789,3891,3988,4086,4193,4302,14015", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3784,3886,3983,4081,4188,4297,4415,14111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57c3c2f9dced1e219da46eae741c7de7\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,13084", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,13159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51876ee9437d313540039b485fb1b11c\\transformed\\play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5825", "endColumns": "126", "endOffsets": "5947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2656d85fa34ff57ee93e0a7799c35d76\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3284,3362,3438,3516,3613,4420,4520,4669,7144,7203,7267,7731,7958,8018,8105,8169,8231,8293,8361,8426,8480,8598,8656,8717,8773,8848,9125,9211,9288,9379,9463,9543,9684,9762,9842,9964,10050,10128,10184,10235,10301,10369,10443,10514,10589,10661,10739,10809,10882,10986,11070,11147,11235,11324,11398,11471,11556,11605,11683,11749,11829,11912,11974,12038,12101,12170,12278,12381,12482,12581,12641,12696,13164,13244,13322", "endLines": "5,36,37,38,39,40,48,49,50,71,72,73,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3357,3433,3511,3608,3688,4515,4664,4742,7198,7262,7348,7799,8013,8100,8164,8226,8288,8356,8421,8475,8593,8651,8712,8768,8843,8969,9206,9283,9374,9458,9538,9679,9757,9837,9959,10045,10123,10179,10230,10296,10364,10438,10509,10584,10656,10734,10804,10877,10981,11065,11142,11230,11319,11393,11466,11551,11600,11678,11744,11824,11907,11969,12033,12096,12165,12273,12376,12477,12576,12636,12691,12771,13239,13317,13394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1f6d4087117984f6265a436ffccadbd8\\transformed\\react-android-0.79.1-debug\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,289,360,443,518,594,675,755,824,902,981,1057,1137,1217,1294,1365,1435,1518,1592,1674", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "126,209,284,355,438,513,589,670,750,819,897,976,1052,1132,1212,1289,1360,1430,1513,1587,1669,1748"}, "to": {"startLines": "33,51,77,79,80,95,96,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2974,4747,7656,7804,7875,8974,9049,12776,12857,12937,13006,13399,13478,13554,13634,13714,13791,13862,13932,14116,14190,14272", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "3045,4825,7726,7870,7953,9044,9120,12852,12932,13001,13079,13473,13549,13629,13709,13786,13857,13927,14010,14185,14267,14346"}}]}]}