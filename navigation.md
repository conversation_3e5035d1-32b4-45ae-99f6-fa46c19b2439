# 📱 Documentation Navigation - React Native App

## 🏗️ Architecture Générale

L'application utilise **React Navigation v7** avec une architecture à deux niveaux :

1. **Niveau Authentification** (`src/App.tsx`)
2. **Niveau Application** (`src/navigation/index.tsx`)

---

## 📁 Structure des Fichiers

```
src/
├── App.tsx                    # Point d'entrée + gestion auth
├── navigation/
│   ├── index.tsx             # Configuration navigation complète
│   └── screens/              # Tous les écrans
│       ├── Home.tsx          # Écran principal (Feed)
│       ├── Login.tsx         # Authentification
│       ├── Profile.tsx       # Profil utilisateur
│       ├── Settings.tsx      # Paramètres (modal)
│       ├── Updates.tsx       # Notifications
│       ├── Agenda.tsx        # Calendrier
│       ├── Drive.tsx         # Stockage fichiers
│       └── NotFound.tsx      # Page 404
```

---

## 🔄 Flux de Navigation

```
📱 App.tsx (Point d'entrée)
├── 🔐 Utilisateur NON connecté
│   └── Login Screen (NavigationContainer simple)
│
└── ✅ Utilisateur CONNECTÉ
    └── Navigation (de navigation/index.tsx)
        └── RootStack
            ├── 📑 AppTabs (Bottom Tabs Navigation)
            │   ├── 🏠 Home (Feed)
            │   ├── 🔔 Updates (Notifications)
            │   ├── 📅 Agenda
            │   └── 📁 Drive
            │
            └── 📄 Stack Screens (Navigation par pile)
                ├── 👤 Profile
                ├── ⚙️ Settings (Modal)
                └── ❌ NotFound (404)
```

---

## 🛠️ Configuration Technique

### **1. App.tsx - Gestion Authentification**

```typescript
// Responsabilité : Authentification uniquement
export function App() {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    FIREBASE_AUTH.onAuthStateChanged((user) => {
      setUser(user);
    });
  }, []);

  if (user) {
    return <Navigation />; // Navigation complète
  }

  return (
    <NavigationContainer>
      <AuthStack.Navigator>
        <AuthStack.Screen name="Login" component={Login} />
      </AuthStack.Navigator>
    </NavigationContainer>
  );
}
```

### **2. navigation/index.tsx - Structure Complète**

```typescript
// Bottom Tabs (Onglets principaux)
const AppTabs = createBottomTabNavigator({
  screens: {
    Home: { screen: Home, options: { title: 'Feed' } },
    Updates: { screen: Updates, options: { title: 'Notifications' } },
    Agenda: { screen: Agenda },
    Drive: { screen: Drive }
  }
});

// Stack Navigation (Écrans modaux/overlay)
const RootStack = createNativeStackNavigator({
  screens: {
    AppTabs: { screen: AppTabs, options: { headerShown: false } },
    Profile: { screen: Profile },
    Settings: { screen: Settings, options: { presentation: 'modal' } },
    NotFound: { screen: NotFound }
  }
});

// Export de la navigation statique
export const Navigation = createStaticNavigation(RootStack);
```

---

## 🎯 Types de Navigation Utilisés

### **Bottom Tab Navigator**
- **Localisation** : Bas de l'écran
- **Écrans** : Home, Updates, Agenda, Drive
- **Icônes** : Personnalisées (newspaper, bell, calendar, folder)
- **Comportement** : Navigation horizontale entre onglets

### **Stack Navigator**
- **Localisation** : Pile d'écrans
- **Écrans** : Profile, Settings, NotFound
- **Comportement** : Navigation verticale (push/pop)

### **Modal Presentation**
- **Écran** : Settings
- **Comportement** : Glisse du bas vers le haut
- **Fermeture** : Bouton "Close" dans le header

---

## 🔗 Navigation Entre Écrans

### **Depuis Home.tsx**
```typescript
<Button screen="Profile">Go to Profile</Button>
<Button screen="Settings">Go to Settings</Button>
```

### **Depuis Profile.tsx**
```typescript
<Button onPress={() => FIREBASE_AUTH.signOut()}>Logout</Button>
```

### **Deep Linking (Profile)**
- **Pattern** : `@username`
- **Exemple** : `/john` → Profile de John
- **Configuration** : Parsing automatique du `@`

---

## 🔧 Fonctionnalités Spéciales

### **Authentification Firebase**
- **Auto-detection** : `onAuthStateChanged`
- **Redirection** : Automatique Login ↔ App
- **Logout** : Depuis Profile → retour Login

### **Static Navigation**
- **Avantage** : Type-safety automatique
- **Performance** : Optimisée pour React Navigation v7
- **TypeScript** : Autocomplétion des routes

### **Icônes Personnalisées**
- **Source** : Assets locaux (png)
- **Couleur** : Dynamique selon l'état (active/inactive)
- **Taille** : Responsive selon la plateforme

---

## 📝 Notes de Développement

### **Bonnes Pratiques Appliquées**
1. **Séparation des responsabilités** : Auth vs Navigation
2. **Configuration centralisée** : Tout dans navigation/index.tsx
3. **Type-safety** : TypeScript + Static Navigation
4. **Modularité** : Écrans séparés dans screens/

### **Points d'Attention**
- Ne pas dupliquer la navigation entre App.tsx et navigation/index.tsx
- Utiliser `Navigation` (pas `RootStack` directement) dans App.tsx
- Les Bottom Tabs sont encapsulés dans le RootStack
- Settings est un modal, pas un onglet

---

## 🚀 Extensions Futures

### **Écrans à Ajouter**
- Statistiques (mentionné dans le cahier des charges)
- Géolocalisation/Présence
- Détails événements (Agenda)
- Upload fichiers (Drive)

### **Améliorations Navigation**
- Animations personnalisées
- Gestes de navigation
- Navigation conditionnelle (rôles utilisateur)
- Cache/Persistence état navigation

---

*Dernière mise à jour : 2025-06-19*
