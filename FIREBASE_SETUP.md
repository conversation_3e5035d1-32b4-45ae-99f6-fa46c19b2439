# Firebase Setup Instructions

## Current Status
✅ Added Google Services plugin to Android build configuration  
✅ Updated Firebase configuration imports  
✅ Created google-services.json template  
❌ **Missing actual google-services.json file**  

## Required Steps to Complete Setup

### 1. Get Your Firebase Configuration File

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project or create a new one
3. Click "Add app" → Android
4. Enter package name: `com.satya164.reactnavigationtemplate`
5. Download `google-services.json`
6. **Replace** `android/app/google-services.json.template` with the actual `google-services.json`

### 2. Enable Firebase Services

In Firebase Console, enable these services:
- **Authentication** (Email/Password provider)
- **Firestore Database**
- **Cloud Messaging**

### 3. Test the Setup

After adding the real `google-services.json`:

```bash
# Clean and rebuild
cd android
./gradlew clean
cd ..
npm run android
```

## Troubleshooting

### If you still get "No Firebase App '[DEFAULT]'" error:

1. **Check google-services.json location**: Must be in `android/app/google-services.json`
2. **Verify package name**: Must match `com.satya164.reactnavigationtemplate`
3. **Clean build**: Run `cd android && ./gradlew clean && cd ..`
4. **Restart Metro**: Stop and restart `npm start`

### Common Issues:

- **Build errors**: Make sure Google Services plugin version is compatible
- **Auth errors**: Enable Email/Password authentication in Firebase Console
- **Messaging errors**: Ensure FCM is enabled in your Firebase project

## Current Configuration

### Android Build Files Updated:
- ✅ `android/build.gradle` - Added Google Services classpath
- ✅ `android/app/build.gradle` - Added Google Services plugin

### Firebase Config:
- ✅ `FirebaseConfig.ts` - Simplified imports (auto-initialization)
- ✅ Services exported: Auth, Messaging, Firestore

### Next Steps:
1. Add real `google-services.json` file
2. Test on Android device/emulator
3. Verify authentication flow works
