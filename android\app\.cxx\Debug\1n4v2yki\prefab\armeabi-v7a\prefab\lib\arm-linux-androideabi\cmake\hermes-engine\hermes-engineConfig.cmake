if(NOT TARGET hermes-engine::libhermes)
add_library(hermes-engine::libhermes SHARED IMPORTED)
set_target_properties(hermes-engine::libhermes PROPERTIES
    IMPORTED_LOCATION "C:/Users/<USER>/.gradle/caches/8.13/transforms/61b2e4e1aa6cbc0eb7af0fb238885ff4/transformed/hermes-android-0.79.1-debug/prefab/modules/libhermes/libs/android.armeabi-v7a/libhermes.so"
    INTERFACE_INCLUDE_DIRECTORIES "C:/Users/<USER>/.gradle/caches/8.13/transforms/61b2e4e1aa6cbc0eb7af0fb238885ff4/transformed/hermes-android-0.79.1-debug/prefab/modules/libhermes/include"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

