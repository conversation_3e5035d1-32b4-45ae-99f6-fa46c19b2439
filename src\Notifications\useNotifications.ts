import { PermissionsAndroid } from 'react-native';
import { useEffect } from 'react';
import messaging from '@react-native-firebase/messaging';

const requestUserPermission = async () => {
  const authStatus = await PermissionsAndroid.request(
    PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
  );

  if (authStatus === PermissionsAndroid.RESULTS.GRANTED) {
    console.log('User granted notifications permission');
  } else if (authStatus === PermissionsAndroid.RESULTS.DENIED) {
    console.log('User denied notifications permission');
  } else if (authStatus === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
    console.log('User denied notifications permission and will not be asked again');
  }
};

const getToken = async () => {
  try {
    const token = await messaging().getToken();
    console.log('FCM token:', token);
  } catch (error) {
    console.log('Error getting token:', error);
  }
};

export function useNotifications() {
  useEffect(() => {
    requestUserPermission();
    getToken();
  }, []);
}
