import { PermissionsAndroid, Platform } from 'react-native';
import { useEffect } from 'react';
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';

const requestUserPermission = async () => {
  if (Platform.OS === 'android') {
    const authStatus = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
    );

    if (authStatus === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('✅ User granted notifications permission');
    } else if (authStatus === PermissionsAndroid.RESULTS.DENIED) {
      console.log('❌ User denied notifications permission');
    } else if (authStatus === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
      console.log('🚫 User denied notifications permission and will not be asked again');
    }
  } else {
    // For iOS, use Firebase messaging permission request
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log('✅ iOS notification permission granted');
    } else {
      console.log('❌ iOS notification permission denied');
    }
  }
};

const getToken = async () => {
  try {
    const token = await messaging().getToken();
    console.log('🔑 FCM token:', token);
    return token;
  } catch (error) {
    console.log('❌ Error getting FCM token:', error);
    return null;
  }
};

const handleForegroundMessage = (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
  console.log('🔔 Foreground message received:', remoteMessage);
  console.log('📱 Message data:', remoteMessage.data);
  console.log('📝 Message notification:', remoteMessage.notification);

  // You can show a custom notification or update UI here
  // For example, you could use a toast library or update app state
};

export function useNotifications() {
  useEffect(() => {
    // Request permissions and get token
    requestUserPermission();
    getToken();

    // Handle foreground messages
    const unsubscribeForeground = messaging().onMessage(handleForegroundMessage);

    // Handle notification opened app (when app is in background/quit)
    const unsubscribeNotificationOpenedApp = messaging().onNotificationOpenedApp(
      (remoteMessage) => {
        console.log('🚀 Notification caused app to open from background:', remoteMessage);
        // Navigate to specific screen based on notification data
        // You can use navigation here if needed
      },
    );

    // Check if app was opened from a notification (when app was completely quit)
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log('🎯 App opened from quit state by notification:', remoteMessage);
          // Navigate to specific screen based on notification data
        }
      });

    // Handle token refresh
    const unsubscribeTokenRefresh = messaging().onTokenRefresh((token) => {
      console.log('🔄 FCM token refreshed:', token);
      // Send the new token to your server
    });

    // Cleanup subscriptions
    return () => {
      unsubscribeForeground();
      unsubscribeNotificationOpenedApp();
      unsubscribeTokenRefresh();
    };
  }, []);
}
