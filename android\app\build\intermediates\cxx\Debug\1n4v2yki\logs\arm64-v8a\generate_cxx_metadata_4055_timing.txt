# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 35ms
  generate-prefab-packages
    exec-prefab 381ms
  generate-prefab-packages completed in 397ms
  execute-generate-process
    exec-configure 436ms
    [gap of 213ms]
  execute-generate-process completed in 649ms
  [gap of 34ms]
  remove-unexpected-so-files 10ms
  [gap of 62ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 1213ms

