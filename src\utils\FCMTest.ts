import messaging from '@react-native-firebase/messaging';

/**
 * Test FCM setup and permissions
 */
export const testFCMSetup = async () => {
  try {
    console.log('🔔 Testing FCM setup...');
    
    // Check if messaging is available
    console.log('📱 FCM Service:', messaging() ? '✅ Available' : '❌ Not available');
    
    // Check permission status
    const authStatus = await messaging().hasPermission();
    console.log('🔐 Permission status:', authStatus);
    
    // Get FCM token
    try {
      const token = await messaging().getToken();
      console.log('🔑 FCM Token:', token ? '✅ Retrieved' : '❌ Failed to get token');
      console.log('📋 Token (first 20 chars):', token ? token.substring(0, 20) + '...' : 'N/A');
    } catch (tokenError) {
      console.log('❌ Token error:', tokenError);
    }
    
    // Check if background handler is registered
    console.log('🔄 Background handler should be registered in index.tsx');
    
    console.log('🎉 FCM test completed!');
    return true;
  } catch (error) {
    console.error('❌ FCM test failed:', error);
    return false;
  }
};

/**
 * Test notification permissions specifically
 */
export const testNotificationPermissions = async () => {
  try {
    console.log('🔔 Testing notification permissions...');
    
    const authStatus = await messaging().requestPermission({
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      provisional: false,
      sound: true,
    });
    
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    
    console.log('✅ Permission result:', enabled ? 'Granted' : 'Denied');
    console.log('📊 Auth status code:', authStatus);
    
    return enabled;
  } catch (error) {
    console.error('❌ Permission test failed:', error);
    return false;
  }
};
