# Firebase Cloud Messaging (FCM) Debug Guide

## ✅ What We Fixed

### 1. Background Message Handler Registration
- **Issue**: Background handler was registered AFTER app registration
- **Fix**: Moved `messaging().setBackgroundMessageHandler()` to be called BEFORE `AppRegistry.registerComponent()` in `index.tsx`
- **Why**: FCM requires the background handler to be registered before the app starts

### 2. Firebase v22 Compatibility
- **Issue**: Deprecated API usage causing warnings
- **Fix**: Updated Firebase configuration to use current v22 APIs
- **Changes**: Simplified imports and removed deprecated `getApp()` usage

### 3. Comprehensive Foreground Handling
- **Issue**: Limited foreground notification handling
- **Fix**: Enhanced `useNotifications.ts` with complete FCM lifecycle management
- **Features**: 
  - Foreground message handling
  - Background app open handling
  - Quit state app open handling
  - Token refresh handling
  - Proper cleanup

### 4. GestureHandlerRootView Integration
- **Issue**: Gesture handler conflicts with navigation
- **Fix**: Wrapped app components with `GestureHandlerRootView`

## 🔍 How to Test FCM

### 1. Check Console Logs
When you start the app, you should see:
```
🔥 Testing Firebase initialization...
📧 Firebase Auth: ✅ Initialized
🗄️ Firebase Firestore: ✅ Initialized
📱 Firebase Messaging: ✅ Initialized
🔔 Testing FCM setup...
📱 FCM Service: ✅ Available
🔑 FCM Token: ✅ Retrieved
✅ User granted notifications permission
```

### 2. Test Background Notifications
1. Send a test notification from Firebase Console
2. Put app in background
3. Check logs for: `🔔 Background message received:`

### 3. Test Foreground Notifications
1. Keep app in foreground
2. Send a test notification
3. Check logs for: `🔔 Foreground message received:`

### 4. Test App Opening from Notifications
1. Send notification while app is closed
2. Tap notification to open app
3. Check logs for: `🎯 App opened from quit state by notification:`

## 🚨 Common Issues & Solutions

### Background Handler Not Working
**Symptoms**: No logs when receiving background notifications
**Solutions**:
1. Ensure `index.tsx` registers background handler BEFORE app registration
2. Check that `google-services.json` is in `android/app/` directory
3. Verify Firebase project has FCM enabled

### Foreground Notifications Not Showing
**Symptoms**: Logs show message received but no visual notification
**Solutions**:
1. Foreground messages don't show system notifications by default
2. Implement custom notification display in `handleForegroundMessage`
3. Consider using a toast library or custom UI

### Permission Issues
**Symptoms**: Token not generated or permission denied
**Solutions**:
1. Check Android notification permissions in device settings
2. For Android 13+, ensure `POST_NOTIFICATIONS` permission is granted
3. Test permission request flow

### Token Not Generated
**Symptoms**: FCM token is null or undefined
**Solutions**:
1. Ensure device has Google Play Services
2. Check internet connection
3. Verify Firebase project configuration
4. Check `google-services.json` is valid

## 📱 Testing Commands

### Send Test Notification via Firebase Console
1. Go to Firebase Console → Cloud Messaging
2. Click "Send your first message"
3. Enter title and body
4. Select your app
5. Send to all users or use your FCM token

### Send Test Notification via curl
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "YOUR_FCM_TOKEN",
    "notification": {
      "title": "Test Notification",
      "body": "This is a test message"
    },
    "data": {
      "custom_key": "custom_value"
    }
  }'
```

## 🔧 Debug Functions

Use these functions in your app to debug FCM:

```typescript
import { testFCMSetup, testNotificationPermissions } from './src/utils/FCMTest';

// Test FCM setup
testFCMSetup();

// Test permissions
testNotificationPermissions();
```

## 📋 Checklist

- [ ] Background handler registered in `index.tsx` before app registration
- [ ] `google-services.json` in `android/app/` directory
- [ ] Google Services plugin applied in `android/app/build.gradle`
- [ ] Notification permissions granted
- [ ] FCM token generated successfully
- [ ] Firebase project has FCM enabled
- [ ] App can receive background notifications
- [ ] App can receive foreground notifications
- [ ] App opens correctly from notifications

## 🎯 Next Steps

1. **Test the current setup** by running the app and checking console logs
2. **Send test notifications** from Firebase Console
3. **Verify all notification scenarios** work (background, foreground, app opening)
4. **Implement custom notification UI** for foreground messages if needed
5. **Add navigation logic** based on notification data
