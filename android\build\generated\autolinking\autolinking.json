{"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative", "reactNativePath": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native", "dependencies": {"@react-native-firebase/app": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\app", "name": "@react-native-firebase/app", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\app\\android", "packageImportPath": "import io.invertase.firebase.app.ReactNativeFirebaseAppPackage;", "packageInstance": "new ReactNativeFirebaseAppPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/@react-native-firebase/app/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/auth": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\auth", "name": "@react-native-firebase/auth", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\auth\\android", "packageImportPath": "import io.invertase.firebase.auth.ReactNativeFirebaseAuthPackage;", "packageInstance": "new ReactNativeFirebaseAuthPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/@react-native-firebase/auth/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/firestore": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\firestore", "name": "@react-native-firebase/firestore", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\firestore\\android", "packageImportPath": "import io.invertase.firebase.firestore.ReactNativeFirebaseFirestorePackage;", "packageInstance": "new ReactNativeFirebaseFirestorePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/@react-native-firebase/firestore/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/messaging": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\messaging", "name": "@react-native-firebase/messaging", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\@react-native-firebase\\messaging\\android", "packageImportPath": "import io.invertase.firebase.messaging.ReactNativeFirebaseMessagingPackage;", "packageInstance": "new ReactNativeFirebaseMessagingPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/@react-native-firebase/messaging/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.satya164.reactnavigationtemplate", "sourceDir": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android"}}}