  Application android.app  Build android.app.Activity  BuildConfig android.app.Activity  DefaultReactActivityDelegate android.app.Activity  ReactActivityDelegateWrapper android.app.Activity  SplashScreenManager android.app.Activity  
fabricEnabled android.app.Activity  moveTaskToBack android.app.Activity  onCreate android.app.Activity  registerOnActivity android.app.Activity  ApplicationLifecycleDispatcher android.app.Application  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  ReactNativeHostWrapper android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  createReactHost android.app.Application  load android.app.Application  onApplicationCreate android.app.Application  onConfigurationChanged android.app.Application  onCreate android.app.Application  ApplicationLifecycleDispatcher android.content.Context  Boolean android.content.Context  Build android.content.Context  BuildConfig android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  OpenSourceMergedSoMapping android.content.Context  PackageList android.content.Context  ReactActivityDelegateWrapper android.content.Context  ReactNativeHostWrapper android.content.Context  ReactPackage android.content.Context  SoLoader android.content.Context  SplashScreenManager android.content.Context  String android.content.Context  createReactHost android.content.Context  
fabricEnabled android.content.Context  load android.content.Context  onApplicationCreate android.content.Context  onConfigurationChanged android.content.Context  registerOnActivity android.content.Context  ApplicationLifecycleDispatcher android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  ReactActivityDelegateWrapper android.content.ContextWrapper  ReactNativeHostWrapper android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  SoLoader android.content.ContextWrapper  SplashScreenManager android.content.ContextWrapper  String android.content.ContextWrapper  applicationContext android.content.ContextWrapper  createReactHost android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  load android.content.ContextWrapper  onApplicationCreate android.content.ContextWrapper  onConfigurationChanged android.content.ContextWrapper  registerOnActivity android.content.ContextWrapper  
Configuration android.content.res  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  Build  android.view.ContextThemeWrapper  BuildConfig  android.view.ContextThemeWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  ReactActivityDelegateWrapper  android.view.ContextThemeWrapper  SplashScreenManager  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  registerOnActivity  android.view.ContextThemeWrapper  Build #androidx.activity.ComponentActivity  BuildConfig #androidx.activity.ComponentActivity  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  ReactActivityDelegateWrapper #androidx.activity.ComponentActivity  SplashScreenManager #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  registerOnActivity #androidx.activity.ComponentActivity  Build (androidx.appcompat.app.AppCompatActivity  BuildConfig (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  ReactActivityDelegateWrapper (androidx.appcompat.app.AppCompatActivity  SplashScreenManager (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  registerOnActivity (androidx.appcompat.app.AppCompatActivity  Build #androidx.core.app.ComponentActivity  BuildConfig #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegateWrapper #androidx.core.app.ComponentActivity  SplashScreenManager #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  registerOnActivity #androidx.core.app.ComponentActivity  Build &androidx.fragment.app.FragmentActivity  BuildConfig &androidx.fragment.app.FragmentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  ReactActivityDelegateWrapper &androidx.fragment.app.FragmentActivity  SplashScreenManager &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  registerOnActivity &androidx.fragment.app.FragmentActivity  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  Build  com.facebook.react.ReactActivity  BuildConfig  com.facebook.react.ReactActivity  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  ReactActivityDelegateWrapper  com.facebook.react.ReactActivity  SplashScreenManager  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  invokeDefaultOnBackPressed  com.facebook.react.ReactActivity  onCreate  com.facebook.react.ReactActivity  registerOnActivity  com.facebook.react.ReactActivity  
fabricEnabled (com.facebook.react.ReactActivityDelegate  mainComponentName (com.facebook.react.ReactActivityDelegate  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  OpenSourceMergedSoMapping com.facebook.react.soloader  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  Application $com.satya164.reactnavigationtemplate  ApplicationLifecycleDispatcher $com.satya164.reactnavigationtemplate  Boolean $com.satya164.reactnavigationtemplate  Build $com.satya164.reactnavigationtemplate  BuildConfig $com.satya164.reactnavigationtemplate  Bundle $com.satya164.reactnavigationtemplate  
Configuration $com.satya164.reactnavigationtemplate  DefaultReactActivityDelegate $com.satya164.reactnavigationtemplate  DefaultReactNativeHost $com.satya164.reactnavigationtemplate  List $com.satya164.reactnavigationtemplate  MainActivity $com.satya164.reactnavigationtemplate  MainApplication $com.satya164.reactnavigationtemplate  OpenSourceMergedSoMapping $com.satya164.reactnavigationtemplate  PackageList $com.satya164.reactnavigationtemplate  
ReactActivity $com.satya164.reactnavigationtemplate  ReactActivityDelegate $com.satya164.reactnavigationtemplate  ReactActivityDelegateWrapper $com.satya164.reactnavigationtemplate  ReactApplication $com.satya164.reactnavigationtemplate  	ReactHost $com.satya164.reactnavigationtemplate  ReactNativeHost $com.satya164.reactnavigationtemplate  ReactNativeHostWrapper $com.satya164.reactnavigationtemplate  ReactPackage $com.satya164.reactnavigationtemplate  SoLoader $com.satya164.reactnavigationtemplate  SplashScreenManager $com.satya164.reactnavigationtemplate  String $com.satya164.reactnavigationtemplate  createReactHost $com.satya164.reactnavigationtemplate  
fabricEnabled $com.satya164.reactnavigationtemplate  load $com.satya164.reactnavigationtemplate  onApplicationCreate $com.satya164.reactnavigationtemplate  onConfigurationChanged $com.satya164.reactnavigationtemplate  registerOnActivity $com.satya164.reactnavigationtemplate  DEBUG 0com.satya164.reactnavigationtemplate.BuildConfig  IS_HERMES_ENABLED 0com.satya164.reactnavigationtemplate.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED 0com.satya164.reactnavigationtemplate.BuildConfig  Build 1com.satya164.reactnavigationtemplate.MainActivity  BuildConfig 1com.satya164.reactnavigationtemplate.MainActivity  ReactActivityDelegateWrapper 1com.satya164.reactnavigationtemplate.MainActivity  SplashScreenManager 1com.satya164.reactnavigationtemplate.MainActivity  
fabricEnabled 1com.satya164.reactnavigationtemplate.MainActivity  mainComponentName 1com.satya164.reactnavigationtemplate.MainActivity  moveTaskToBack 1com.satya164.reactnavigationtemplate.MainActivity  registerOnActivity 1com.satya164.reactnavigationtemplate.MainActivity  ApplicationLifecycleDispatcher 4com.satya164.reactnavigationtemplate.MainApplication  BuildConfig 4com.satya164.reactnavigationtemplate.MainApplication  OpenSourceMergedSoMapping 4com.satya164.reactnavigationtemplate.MainApplication  PackageList 4com.satya164.reactnavigationtemplate.MainApplication  ReactNativeHostWrapper 4com.satya164.reactnavigationtemplate.MainApplication  SoLoader 4com.satya164.reactnavigationtemplate.MainApplication  applicationContext 4com.satya164.reactnavigationtemplate.MainApplication  createReactHost 4com.satya164.reactnavigationtemplate.MainApplication  load 4com.satya164.reactnavigationtemplate.MainApplication  onApplicationCreate 4com.satya164.reactnavigationtemplate.MainApplication  onConfigurationChanged 4com.satya164.reactnavigationtemplate.MainApplication  reactNativeHost 4com.satya164.reactnavigationtemplate.MainApplication  ApplicationLifecycleDispatcher expo.modules  ReactActivityDelegateWrapper expo.modules  ReactNativeHostWrapper expo.modules  onApplicationCreate +expo.modules.ApplicationLifecycleDispatcher  onConfigurationChanged +expo.modules.ApplicationLifecycleDispatcher  	Companion #expo.modules.ReactNativeHostWrapper  createReactHost #expo.modules.ReactNativeHostWrapper  createReactHost -expo.modules.ReactNativeHostWrapper.Companion  SplashScreenManager expo.modules.splashscreen  registerOnActivity -expo.modules.splashscreen.SplashScreenManager  Nothing kotlin  not kotlin.Boolean  	compareTo 
kotlin.Int  List kotlin.collections                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  