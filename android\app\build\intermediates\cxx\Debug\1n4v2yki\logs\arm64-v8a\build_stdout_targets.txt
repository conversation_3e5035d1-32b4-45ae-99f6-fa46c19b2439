ninja: Entering directory `C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\.cxx\Debug\1n4v2yki\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/3] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[2/3] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[3/3] Linking CXX shared library C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\cxx\Debug\1n4v2yki\obj\arm64-v8a\libappmodules.so
