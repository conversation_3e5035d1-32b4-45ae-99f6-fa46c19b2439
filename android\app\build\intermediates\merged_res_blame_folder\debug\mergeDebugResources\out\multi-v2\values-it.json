{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-55:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\25dd2e9a2ec645a6ceb46052888a9d56\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,13350", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,13427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3acfda74680b6b841c8159f43eacaed\\transformed\\play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4897,5002,5153,5278,5386,5544,5672,5792,6032,6189,6296,6450,6577,6733,6914,6981,7042", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "4997,5148,5273,5381,5539,5667,5787,5891,6184,6291,6445,6572,6728,6909,6976,7037,7114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,51,77,79,80,95,96,143,144,145,150,151,152,153,154,155,156,157,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,4813,7755,7911,7983,9113,9193,13091,13181,13262,13678,13764,13841,13921,14000,14075,14145,14214,14405,14480,14561", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3077,4892,7821,7978,8066,9188,9272,13176,13257,13345,13759,13836,13916,13995,14070,14140,14209,14299,14475,14556,14643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\480ce62020c830cf93653ad6a17bbf11\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "41,42,43,44,45,46,47,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3744,3842,3944,4043,4145,4254,4361,14304", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3837,3939,4038,4140,4249,4356,4486,14400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\582d0c2f5e6292ce32dc808d4c3bbb90\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7119,7444,7542,7652", "endColumns": "99,97,109,102", "endOffsets": "7214,7537,7647,7750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\429888db8e5916503c7ec8b30991ae80\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3308,3392,3473,3550,3649,4491,4590,4730,7219,7283,7349,7826,8071,8133,8221,8283,8352,8415,8488,8551,8605,8726,8783,8845,8899,8976,9277,9362,9442,9541,9627,9709,9844,9925,10006,10152,10243,10333,10388,10439,10505,10578,10658,10729,10809,10884,10961,11030,11107,11212,11300,11389,11482,11575,11649,11729,11823,11874,11958,12024,12108,12196,12258,12322,12385,12453,12568,12682,12788,12897,12956,13011,13432,13517,13596", "endLines": "5,36,37,38,39,40,48,49,50,71,72,73,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,147,148,149", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "313,3387,3468,3545,3644,3739,4585,4725,4808,7278,7344,7439,7906,8128,8216,8278,8347,8410,8483,8546,8600,8721,8778,8840,8894,8971,9108,9357,9437,9536,9622,9704,9839,9920,10001,10147,10238,10328,10383,10434,10500,10573,10653,10724,10804,10879,10956,11025,11102,11207,11295,11384,11477,11570,11644,11724,11818,11869,11953,12019,12103,12191,12253,12317,12380,12448,12563,12677,12783,12892,12951,13006,13086,13512,13591,13673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63e389f272c6144302a9e6a3862bfc64\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5896", "endColumns": "135", "endOffsets": "6027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2763bc7439cd222ae35e1433f4611677\\transformed\\credentials-1.2.0-rc01\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3082,3194", "endColumns": "111,113", "endOffsets": "3189,3303"}}]}]}