[{"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\79b7bec8534d0488d02d2214d6959394\\proto-reactnative\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\RNEdgeToEdge-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\Props.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\States.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a5b71bb0bb8449bf5cb873f344c900de\\RNCSafeAreaViewShadowNode.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a5b71bb0bb8449bf5cb873f344c900de\\RNCSafeAreaViewState.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\821b3ceb3b53da2645150b406a392297\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\dc06243f85d8f6463ad2e4fbe181fc68\\safeareacontext\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ffca5788ae2511e958786a570a961d13\\components\\safeareacontext\\Props.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\dc06243f85d8f6463ad2e4fbe181fc68\\safeareacontext\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ffca5788ae2511e958786a570a961d13\\components\\safeareacontext\\States.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\821b3ceb3b53da2645150b406a392297\\safeareacontextJSI-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\06b22b1b30f144089ac051da8bb67ffe\\jni\\safeareacontext-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f81dfcddb790e34a72eaa5b61c0bf91a\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c7805c2643d52f64fd1db6e5aaee5bf3\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c7805c2643d52f64fd1db6e5aaee5bf3\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d3909333b9fa80344619c6ba5e10a007\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f81dfcddb790e34a72eaa5b61c0bf91a\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d3909333b9fa80344619c6ba5e10a007\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f81dfcddb790e34a72eaa5b61c0bf91a\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\61c24ba14db9fe7ad90891edb7913c7d\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\74aa438f27a0f3eb8d9862433e72106f\\components\\rnscreens\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6f87e64fc07b28e40596b577dcff19a5\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\db06d6068da9a843e25959b212b84c4c\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6f87e64fc07b28e40596b577dcff19a5\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e33a6dc7c1e01eb8f675429f3b256269\\react\\renderer\\components\\rnscreens\\States.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1f6d4087117984f6265a436ffccadbd8/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/807303d174752a9c079100d8dd463ef3/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\74aa438f27a0f3eb8d9862433e72106f\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}]