import { Button, Text } from '@react-navigation/elements';
import { StyleSheet, View } from 'react-native';
import { FIREBASE_AUTH } from '../../../FirebaseConfig';
import { FirebaseAuthTypes } from '@react-native-firebase/auth';

export function Profile() {
  const user = FIREBASE_AUTH.currentUser as FirebaseAuthTypes.User;
  return (
    <View style={styles.container}>
      <Text>{user.email}'s Profile</Text>
      <Button onPress={() => FIREBASE_AUTH.signOut()}>Logout</Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
});
