{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-55:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\25dd2e9a2ec645a6ceb46052888a9d56\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,5248", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,5329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\582d0c2f5e6292ce32dc808d4c3bbb90\\transformed\\browser-1.6.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "40,41,42,43", "startColumns": "4,4,4,4", "startOffsets": "3965,4080,4179,4291", "endColumns": "114,98,111,105", "endOffsets": "4075,4174,4286,4392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,211,282,352,435,502,569,648,727,815,908,976,1062,1147,1223,1306,1388,1463,1541,1615,1701,1773,1852,1928", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "120,206,277,347,430,497,564,643,722,810,903,971,1057,1142,1218,1301,1383,1458,1536,1610,1696,1768,1847,1923,2009"}, "to": {"startLines": "29,39,44,45,46,47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2843,3879,4397,4468,4538,4621,4688,4755,4834,4913,5001,5094,5162,5334,5419,5495,5578,5660,5735,5813,5887,6074,6146,6225,6301", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "2908,3960,4463,4533,4616,4683,4750,4829,4908,4996,5089,5157,5243,5414,5490,5573,5655,5730,5808,5882,5968,6141,6220,6296,6382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\480ce62020c830cf93653ad6a17bbf11\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "32,33,34,35,36,37,38,64", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3144,3241,3343,3442,3542,3649,3759,5973", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3236,3338,3437,3537,3644,3754,3874,6069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2763bc7439cd222ae35e1433f4611677\\transformed\\credentials-1.2.0-rc01\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "30,31", "startColumns": "4,4", "startOffsets": "2913,3022", "endColumns": "108,121", "endOffsets": "3017,3139"}}]}]}