{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-56:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51876ee9437d313540039b485fb1b11c\\transformed\\play-services-basement-18.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5357", "endColumns": "102", "endOffsets": "5455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2656d85fa34ff57ee93e0a7799c35d76\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3146,3210,3272,3339,3409,4147,4241,4348,6396,6447,6509,6940,7141,7266,7344,7405,7463,7519,7579,7637,7691,7776,7832,7890,7944,8009,8309,8383,8455,8537,8611,8688,8808,8871,8934,9033,9110,9184,9234,9285,9351,9414,9482,9553,9624,9685,9756,9823,9885,9972,10051,10116,10199,10284,10358,10422,10498,10546,10619,10683,10759,10837,10899,10963,11026,11092,11172,11250,11326,11405,11459,11514,11947,12022,12095", "endLines": "5,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,3205,3267,3334,3404,3481,4236,4343,4416,6442,6504,6582,6995,7196,7339,7400,7458,7514,7574,7632,7686,7771,7827,7885,7939,8004,8096,8378,8450,8532,8606,8683,8803,8866,8929,9028,9105,9179,9229,9280,9346,9409,9477,9548,9619,9680,9751,9818,9880,9967,10046,10111,10194,10279,10353,10417,10493,10541,10614,10678,10754,10832,10894,10958,11021,11087,11167,11245,11321,11400,11454,11509,11578,12017,12090,12160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1f6d4087117984f6265a436ffccadbd8\\transformed\\react-android-0.79.1-debug\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,532,602,674,747,822,889,959,1032,1104,1181,1257,1329,1399,1468,1548,1616,1686,1753", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,527,597,669,742,817,884,954,1027,1099,1176,1252,1324,1394,1463,1543,1611,1681,1748,1817"}, "to": {"startLines": "33,51,77,79,80,82,96,97,98,145,146,147,148,153,154,155,156,157,158,159,160,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,4421,6873,7000,7066,7201,8101,8167,8237,11583,11656,11731,11798,12165,12238,12310,12387,12463,12535,12605,12674,12855,12923,12993,13060", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2924,4488,6935,7061,7136,7261,8162,8232,8304,11651,11726,11793,11863,12233,12305,12382,12458,12530,12600,12669,12749,12918,12988,13055,13124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f320f406cab317ad471420607f539f77\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "41,42,43,44,45,46,47,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3486,3578,3677,3771,3865,3958,4051,12754", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3573,3672,3766,3860,3953,4046,4142,12850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57c3c2f9dced1e219da46eae741c7de7\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,11868", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,11942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a04f17c587cd6ce1f2fc9c2917a06c1d\\transformed\\credentials-1.2.0-rc01\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "2929,3035", "endColumns": "105,110", "endOffsets": "3030,3141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e40193e98f0e5a09a490d3ba62d94d70\\transformed\\browser-1.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6312,6587,6679,6780", "endColumns": "83,91,100,92", "endOffsets": "6391,6674,6775,6868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a4306a6f482b9baf272025916cdd1d72\\transformed\\play-services-base-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4493,4594,4722,4837,4939,5046,5162,5262,5460,5570,5671,5800,5915,6017,6125,6181,6238", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "4589,4717,4832,4934,5041,5157,5257,5352,5565,5666,5795,5910,6012,6120,6176,6233,6307"}}]}]}