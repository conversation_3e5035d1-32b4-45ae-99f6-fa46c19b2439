# ninja log v5
62	5236	7724711030825763	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	b002e336175386c1
81	4573	7724711024228676	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a9f5804c773411b172a3ad857663f85b/safeareacontext/RNCSafeAreaViewState.cpp.o	68d2a642e30876a4
1	16	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/x86_64/CMakeFiles/cmake.verify_globs	35b87b5b2e3335c8
86	3238	7724711009690216	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/States.cpp.o	c8e4125562f62722
49	2931	7724711007098630	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	d4df6031fefc7091
7637	7725	7724711055688985	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_rnscreens.so	fe20b6b139518177
37	3399	7724711012040639	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	fef11db6cd6d588b
19	3196	7724711010233840	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	e76024572c818764
11	8335	7724711061425954	CMakeFiles/appmodules.dir/C_/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	1774a3713e9d2b0
113	4380	7724711022258990	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	d6239ddf1ab30de7
33	2939	7724711007138629	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	3a9839fe37ae805b
66	4310	7724711021509563	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	f4d3363830578375
75	3501	7724711011900392	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	d419d0c46dc1d047
3400	6380	7724711042376234	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/ShadowNodes.cpp.o	241db62c8aae9abe
22	3567	7724711013470838	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	c84dbc6bc53763f1
2940	6591	7724711044394109	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/Props.cpp.o	5a31bc1258493919
25	3844	7724711016691016	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	a12e8742b43109db
45	3609	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	4431d03ebda97f3
96	3982	7724711018154344	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	de019159cfe32153
143	3876	7724711016943494	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/61c24ba14db9fe7ad90891edb7913c7d/renderer/components/rnscreens/RNSScreenState.cpp.o	b03e0625b00030b2
148	4004	7724711018475496	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	f524fdabeda68a78
29	3980	7724711018189539	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	b0d62bca13773694
58	3998	7724711018224590	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	f75854decf9d2d0b
14	3799	7724711016164439	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	c06ada637ae630fc
163	4118	7724711019553203	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	6117b665be7122ea
137	5049	7724711028981560	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	606608e7a8fd1084
16	4553	7724711024017257	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	700af9a4f27dde4a
8	4507	7724711023512253	CMakeFiles/appmodules.dir/OnLoad.cpp.o	64520768eb4c506c
102	4956	7724711028023041	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/Props.cpp.o	981d0d570e422f64
131	5040	7724711028886161	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	8cbf8c13d1bcb607
119	4817	7724711026688503	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5b2a5558f6330fea
91	4764	7724711026155334	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	7a177471de32779f
3501	5978	7724711038334536	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	858db1815187ae27
108	4965	7724711028130413	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSModalScreenShadowNode.cpp.o	c3429b76bdf11255
125	5096	7724711029436317	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	e5bf5faffbd674b6
54	4719	7724711025568779	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	c31bf33a0da6a922
40	5387	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	3cd98be765967618
70	5458	7724711033069714	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ComponentDescriptors.cpp.o	2cd21fa25c58bd47
3239	5582	7724711034386281	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/States.cpp.o	ec579de83b45ed22
5458	5545	7724711033930862	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_safeareacontext.so	5c280228ee7ecbe0
168	5234	7724711030845771	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	13434d10eb365c5f
2932	7637	7724711054741134	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	e3200202954e9db2
3196	7011	7724711048572539	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	cb6ad4773c017a0f
8335	8456	7724711062998568	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libappmodules.so	f8f6840a029b9db0
2	30	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/x86_64/CMakeFiles/cmake.verify_globs	35b87b5b2e3335c8
52	3626	7724718370157774	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	13cb475c05572fd
83	3767	7724718371145876	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	475d6828be44f512
148	3876	7724718372315427	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/States.cpp.o	3caafecc17e96c65
28	4069	7724718374628916	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	c326f72476992e90
57	4140	7724718375238137	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	b31b437e4ce42149
126	4574	7724718379041489	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	4fe404c834930dbd
47	4582	7724718379203834	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	48b13b21d4399924
25	4584	7724718379213953	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	eaff7a53d983f218
90	4585	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	1222e5701de8554a
38	4587	7724718379423140	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	c96b09156c3f3afa
140	4590	7724718379483200	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	bdb4fcf6be3cacb8
33	4733	7724718381341061	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	b65dc5c82430ffd1
162	4762	7724718381578224	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	c8f3801565a8a717
192	4803	7724718381948675	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/61c24ba14db9fe7ad90891edb7913c7d/renderer/components/rnscreens/RNSScreenState.cpp.o	f1fddc76586033d7
200	4887	7724718382919981	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	3bacf4d2d070b6e8
78	4916	7724718383271059	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	f36f538d09f23c27
223	4963	7724718383726010	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	83535d4765093e17
42	5044	7724718384539108	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	47092d96b3fbc4d0
103	5058	7724718384659342	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	a59da458cb146720
116	5246	7724718386586708	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a9f5804c773411b172a3ad857663f85b/safeareacontext/RNCSafeAreaViewState.cpp.o	290eb567e3ffeb6b
155	5366	7724718387751960	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/Props.cpp.o	9cc5686d0f06271c
17	5415	7724718388187209	CMakeFiles/appmodules.dir/OnLoad.cpp.o	af7d878e4cc15162
133	5435	7724718388455992	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	76533c58693db6dc
178	5440	7724718388553031	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	f91fae46890db7a8
231	5642	7724718390514124	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	a5cf12d189173fb
170	5682	7724718390913212	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSModalScreenShadowNode.cpp.o	3108c25e97d1777b
185	5699	7724718391058047	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	b7df72cb0b96cd4d
209	5719	7724718391280521	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	591b54e555f2ead2
216	5721	7724718391345817	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	39c92c198a8b3604
71	5742	7724718391410954	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	c0706e47b1d8c45b
96	5983	7724718393949501	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	527629f520fb191e
64	6053	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	62009e81da234731
3876	6177	7724718395944953	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/States.cpp.o	7184c94358807ca7
109	6269	7724718396787115	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ComponentDescriptors.cpp.o	83841a173d2e0647
6270	6388	7724718397970983	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_safeareacontext.so	61ffd478da64a1cd
4574	6892	7724718403085256	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	b7d12a09626666f7
4069	7103	7724718405188739	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/ShadowNodes.cpp.o	5e3e95e89544be38
3767	7333	7724718407413884	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/Props.cpp.o	666dd08c52cef778
4140	7792	7724718411989848	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	25ff9cd06641ca6b
3628	8389	7724718417891729	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	6660d64d552234be
8389	8489	7724718418962834	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_rnscreens.so	62c14ceb8af00e7f
21	9088	7724718424674297	CMakeFiles/appmodules.dir/C_/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	dd5722098e2b4830
9088	9209	7724718426162661	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libappmodules.so	8908c31199e6f83c
