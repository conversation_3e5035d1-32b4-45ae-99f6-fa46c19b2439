# ninja log v5
91	3058	7724708324036524	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/States.cpp.o	c8e4125562f62722
76	5175	7724708345270288	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	b002e336175386c1
69	4390	7724708337442193	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a9f5804c773411b172a3ad857663f85b/safeareacontext/RNCSafeAreaViewState.cpp.o	68d2a642e30876a4
2	17	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/x86_64/CMakeFiles/cmake.verify_globs	35b87b5b2e3335c8
6856	6944	7724708362907684	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_rnscreens.so	fe20b6b139518177
39	3443	7724708327335384	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	fef11db6cd6d588b
43	2874	7724708321407289	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	d4df6031fefc7091
2	8229	7724708375477520	CMakeFiles/appmodules.dir/C_/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	1774a3713e9d2b0
132	4037	7724708333718237	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	d6239ddf1ab30de7
15	3239	7724708325838663	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	e76024572c818764
58	3972	7724708333002520	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	f4d3363830578375
22	2890	7724708321832201	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	3a9839fe37ae805b
3240	6237	7724708355960492	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/ShadowNodes.cpp.o	241db62c8aae9abe
87	3612	7724708328902047	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	d419d0c46dc1d047
3444	6669	7724708360175356	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/Props.cpp.o	5a31bc1258493919
30	3795	7724708331266544	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	c84dbc6bc53763f1
11	3620	7724708329482915	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	a12e8742b43109db
10	1445	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	4431d03ebda97f3
110	3910	7724708332312981	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	de019159cfe32153
156	3952	7724708332972526	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/61c24ba14db9fe7ad90891edb7913c7d/renderer/components/rnscreens/RNSScreenState.cpp.o	b03e0625b00030b2
139	3970	7724708332992519	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	f524fdabeda68a78
26	4081	7724708334353751	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	b0d62bca13773694
8	4139	7724708334884357	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	c06ada637ae630fc
52	4111	7724708334584042	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	f75854decf9d2d0b
145	4857	7724708342056372	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	606608e7a8fd1084
150	4184	7724708335324797	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	6117b665be7122ea
19	4232	7724708335810271	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	700af9a4f27dde4a
5	4602	7724708339559667	CMakeFiles/appmodules.dir/OnLoad.cpp.o	64520768eb4c506c
163	4949	7724708343017313	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	8cbf8c13d1bcb607
81	4709	7724708340555775	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/Props.cpp.o	981d0d570e422f64
127	4758	7724708341091270	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5b2a5558f6330fea
3612	6028	7724708353823643	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	858db1815187ae27
115	4810	7724708341641939	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	7a177471de32779f
121	4859	7724708342086388	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSModalScreenShadowNode.cpp.o	c3429b76bdf11255
169	4931	7724708342852146	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	e5bf5faffbd674b6
47	4947	7724708342867186	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	c31bf33a0da6a922
7	2151	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	3cd98be765967618
96	5357	7724708347048364	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ComponentDescriptors.cpp.o	2cd21fa25c58bd47
3058	5364	7724708347213492	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/States.cpp.o	ec579de83b45ed22
5357	5466	7724708348154656	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_safeareacontext.so	5c280228ee7ecbe0
2890	6324	7724708356821277	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	13434d10eb365c5f
175	6595	7724708359329740	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	e3200202954e9db2
2882	6856	7724708362036722	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	cb6ad4773c017a0f
2151	2271	7724709977959825	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libappmodules.so	f8f6840a029b9db0
1	16	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/x86_64/CMakeFiles/cmake.verify_globs	35b87b5b2e3335c8
49	2931	7724711007098630	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	d4df6031fefc7091
33	2939	7724711007138629	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	3a9839fe37ae805b
19	3196	7724711010233840	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	e76024572c818764
86	3238	7724711009690216	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/States.cpp.o	c8e4125562f62722
37	3399	7724711012040639	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	fef11db6cd6d588b
75	3501	7724711011900392	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	d419d0c46dc1d047
22	3567	7724711013470838	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	c84dbc6bc53763f1
45	3609	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	4431d03ebda97f3
14	3799	7724711016164439	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	c06ada637ae630fc
25	3844	7724711016691016	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	a12e8742b43109db
143	3876	7724711016943494	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/61c24ba14db9fe7ad90891edb7913c7d/renderer/components/rnscreens/RNSScreenState.cpp.o	b03e0625b00030b2
29	3980	7724711018189539	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	b0d62bca13773694
96	3982	7724711018154344	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	de019159cfe32153
58	3998	7724711018224590	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	f75854decf9d2d0b
148	4004	7724711018475496	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	f524fdabeda68a78
163	4118	7724711019553203	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	6117b665be7122ea
66	4310	7724711021509563	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	f4d3363830578375
113	4380	7724711022258990	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	d6239ddf1ab30de7
8	4507	7724711023512253	CMakeFiles/appmodules.dir/OnLoad.cpp.o	64520768eb4c506c
16	4553	7724711024017257	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	700af9a4f27dde4a
81	4573	7724711024228676	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a9f5804c773411b172a3ad857663f85b/safeareacontext/RNCSafeAreaViewState.cpp.o	68d2a642e30876a4
54	4719	7724711025568779	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	c31bf33a0da6a922
91	4764	7724711026155334	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	7a177471de32779f
119	4817	7724711026688503	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5b2a5558f6330fea
102	4956	7724711028023041	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/Props.cpp.o	981d0d570e422f64
108	4965	7724711028130413	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSModalScreenShadowNode.cpp.o	c3429b76bdf11255
131	5040	7724711028886161	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	8cbf8c13d1bcb607
137	5049	7724711028981560	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	606608e7a8fd1084
125	5096	7724711029436317	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	e5bf5faffbd674b6
168	5234	7724711030845771	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	13434d10eb365c5f
62	5236	7724711030825763	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	b002e336175386c1
40	5387	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	3cd98be765967618
70	5458	7724711033069714	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ComponentDescriptors.cpp.o	2cd21fa25c58bd47
5458	5545	7724711033930862	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_safeareacontext.so	5c280228ee7ecbe0
3239	5582	7724711034386281	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/States.cpp.o	ec579de83b45ed22
3501	5978	7724711038334536	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	858db1815187ae27
3400	6380	7724711042376234	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/ShadowNodes.cpp.o	241db62c8aae9abe
2940	6591	7724711044394109	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/Props.cpp.o	5a31bc1258493919
3196	7011	7724711048572539	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	cb6ad4773c017a0f
2932	7637	7724711054741134	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	e3200202954e9db2
7637	7725	7724711055688985	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libreact_codegen_rnscreens.so	fe20b6b139518177
11	8335	7724711061425954	CMakeFiles/appmodules.dir/C_/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	1774a3713e9d2b0
8335	8456	7724711062998568	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/x86_64/libappmodules.so	f8f6840a029b9db0
