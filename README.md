# 📅 Cahier des charges - Application mobile React Native

## 🌟 Objectif

Créer une **application mobile native** (iOS & Android) avec **React Native**, destinée à des utilisateurs disposant d'un **compte créé à l'avance**, permettant :

- La **connexion via email et mot de passe**
- La **réception de notifications push**
- L'accès à un **agenda dynamique** (inscription à des événements)
- L'accès à un **drive simple** (consultation/téléchargement de fichiers)
- La **consultation de données statistiques**
- (Bonus) Le **signalement de présence par géolocalisation**

---

## ⚙️ Stack technique

### 🔹 Frontend (React Native)

- **Framework** : React Native
- **Navigation** : React Navigation
- **UI** : NativeBase ou React Native Paper (ou composants custom)
- **Authentification** : `@react-native-firebase/auth`
- **Push Notifications** : `@react-native-firebase/messaging`
- **Base de données** : `@react-native-firebase/firestore`
- **Stockage fichiers** : `@react-native-firebase/storage`
- **Agenda** : `react-native-big-calendar` ou `react-native-calendars`
- **Géolocalisation** : `expo-location` ou `@react-native-community/geolocation`

### 🔹 Backend (Firebase)

- Firebase Auth
- Firebase Firestore
- Firebase Cloud Messaging (FCM)
- Firebase Storage
- Firebase Cloud Functions (rappels automatiques, traitement logique)

---

## 🔐 Authentification

- Pas de création de compte dans l’app (comptes pré-créés)
- Connexion via email + mot de passe
- Rôles `admin` / `user` gérés via custom claims ou Firestore Rules
- Authentification persistante via contexte + `onAuthStateChanged`

---

## 🗓️ Agenda dynamique

- Affichage des événements avec code couleur :
  - Vert : inscrit
  - Bleu : disponible
  - Rouge : complet ou désactivé
- Clic sur un événement permet l'inscription (si autorisé)
- Modèle Firestore :

```json
"events": {
  "eventId": {
    "title": "Formation X",
    "type": "prospection" | "inscrit",
    "start": Timestamp,
    "end": Timestamp,
    "maxParticipants": 20,
    "inscriptions": ["uid1", "uid2"]
  }
},

"users": {
  "uid": {
    "name": "John",
    "email": "...",
    "inscriptions": ["eventId1", "eventId2"]
  }
}
```

---

## 🔔 Notifications Push

- Types :
  - Confirmation à l'inscription
  - Rappel 48h avant événement
- Fonctionnement :
  - Enregistrement du token FCM à la connexion
  - Envoi manuel (admin) ou automatique (Cloud Function)

---

## 📂 Drive (fichiers)

- Upload possible uniquement pour les admins (Storage Rules)
- Les utilisateurs peuvent :
  - Voir la liste des fichiers
  - Télécharger les documents
- Interface simple (liste ou cartes)
- Utilisation de `react-native-firebase/storage` + `rn-fetch-blob` ou `expo-file-system`

---

## 📊 Données consultables

- Dashboard de statistiques :
  - Total d’inscriptions
  - Nombre de sessions à venir / passées
  - Répartition des inscrits
  - Liste des prochaines sessions
- Données dynamiques issues de Firestore
- Affichage via composants graphiques ou tableaux

---

## 🌐 Signalement de présence (bonus)

- Détection de présence à proximité d’un site (rayon 100m)
- Utilisation de la géolocalisation native
- Consentement nécessaire
- Donnée de présence envoyée dans Firestore si l’utilisateur est à portée

---

## 📀 Distribution & déploiement

- Publication via App Store / Google Play
- Backend Firebase (Auth, Firestore, Storage, Functions)
- Pas besoin de Firebase Hosting (plus de PWA)

---

## ⚡ Plan de développement possible

1. Setup projet React Native + Firebase
2. Implémentation authentification
3. Affichage Agenda + inscription
4. Notifications push
5. Intégration du Drive
6. Dashboard de données
7. Géolocalisation (bonus)
8. Test & publication sur stores

---

> Ce cahier des charges remplace l’ancienne version PWA et est spécifiquement conçu pour une app **native mobile multiplateforme** via React Native.

