com.satya164.reactnavigationtemplate.app-startup-runtime-1.1.1-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\res
com.satya164.reactnavigationtemplate.app-appcompat-resources-1.7.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\09494fb4bc8f220614094cdf67a9b197\transformed\appcompat-resources-1.7.0\res
com.satya164.reactnavigationtemplate.app-activity-ktx-1.8.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\116c308dcd0af56677669e38e3b2a9e4\transformed\activity-ktx-1.8.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-livedata-core-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\141de2705609c2aefea70a7a6ec96916\transformed\lifecycle-livedata-core-2.6.2\res
com.satya164.reactnavigationtemplate.app-drawerlayout-1.1.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\1727a3cb27ebdef3154cd7c2211b8b98\transformed\drawerlayout-1.1.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-viewmodel-2.6.2-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\1faab334db77e08cdd8e99a4aef0ffa3\transformed\lifecycle-viewmodel-2.6.2\res
com.satya164.reactnavigationtemplate.app-appcompat-1.7.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\25dd2e9a2ec645a6ceb46052888a9d56\transformed\appcompat-1.7.0\res
com.satya164.reactnavigationtemplate.app-credentials-1.2.0-rc01-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\2763bc7439cd222ae35e1433f4611677\transformed\credentials-1.2.0-rc01\res
com.satya164.reactnavigationtemplate.app-fragment-1.6.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2d501250e312faf23654afeb26f10903\transformed\fragment-1.6.1\res
com.satya164.reactnavigationtemplate.app-profileinstaller-1.3.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\res
com.satya164.reactnavigationtemplate.app-transition-1.5.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\379aca9cadd8bb41b8a17f7b6c2df346\transformed\transition-1.5.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-process-2.6.2-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\res
com.satya164.reactnavigationtemplate.app-lifecycle-livedata-core-ktx-2.6.2-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\3e295a72abacc6ce20dc0ca41a996c76\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.satya164.reactnavigationtemplate.app-cardview-1.0.0-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\3ffb45bef6d4684be778fb0b427ccf0e\transformed\cardview-1.0.0\res
com.satya164.reactnavigationtemplate.app-credentials-play-services-auth-1.2.0-rc01-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\416ea1301ddfc377599c62300894f9cb\transformed\credentials-play-services-auth-1.2.0-rc01\res
com.satya164.reactnavigationtemplate.app-emoji2-1.3.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\res
com.satya164.reactnavigationtemplate.app-material-1.12.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\429888db8e5916503c7ec8b30991ae80\transformed\material-1.12.0\res
com.satya164.reactnavigationtemplate.app-core-1.13.1-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\res
com.satya164.reactnavigationtemplate.app-recyclerview-1.1.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\493de7f104d0479b26aa33c67c4bc71a\transformed\recyclerview-1.1.0\res
com.satya164.reactnavigationtemplate.app-firebase-common-21.0.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\res
com.satya164.reactnavigationtemplate.app-swiperefreshlayout-1.1.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\5207d0367f06c1f6e84cbefae3e2b587\transformed\swiperefreshlayout-1.1.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-runtime-2.6.2-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\5567384c0811be2fdff4aa86a52ddb5b\transformed\lifecycle-runtime-2.6.2\res
com.satya164.reactnavigationtemplate.app-core-runtime-2.2.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\55a5428e2997af18cb564ece6f864d2d\transformed\core-runtime-2.2.0\res
com.satya164.reactnavigationtemplate.app-browser-1.6.0-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\582d0c2f5e6292ce32dc808d4c3bbb90\transformed\browser-1.6.0\res
com.satya164.reactnavigationtemplate.app-core-common-2.0.3-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc9c2eaa2b1ec111249f3a67069a0de\transformed\core-common-2.0.3\res
com.satya164.reactnavigationtemplate.app-play-services-basement-18.4.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\63e389f272c6144302a9e6a3862bfc64\transformed\play-services-basement-18.4.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-viewmodel-savedstate-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\645235fd5f58e6da3a574b83261b74f1\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.satya164.reactnavigationtemplate.app-emoji2-views-helper-1.3.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\6453ecd10fa736e3e1a54637aa44062a\transformed\emoji2-views-helper-1.3.0\res
com.satya164.reactnavigationtemplate.app-expo.modules.systemui-5.0.9-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\6758a190ca1d7a837e6d3ae5c00ca112\transformed\expo.modules.systemui-5.0.9\res
com.satya164.reactnavigationtemplate.app-core-ktx-1.13.1-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\67edbce12d482f99ad850ac6e27ba795\transformed\core-ktx-1.13.1\res
com.satya164.reactnavigationtemplate.app-annotation-experimental-1.4.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\6db73666d44fc921249f2da839274405\transformed\annotation-experimental-1.4.0\res
com.satya164.reactnavigationtemplate.app-play-services-auth-21.3.0-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\719943914f8bc52ce030e29929a7c4e7\transformed\play-services-auth-21.3.0\res
com.satya164.reactnavigationtemplate.app-react-android-0.79.1-debug-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\7324ab0e624d5e4aa160cd354b3f9221\transformed\react-android-0.79.1-debug\res
com.satya164.reactnavigationtemplate.app-lifecycle-viewmodel-ktx-2.6.2-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\7a95eb76eac15d8949b90e2b41aa5f8b\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.satya164.reactnavigationtemplate.app-media-1.0.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\7df43354cc9c88f43706e2de371bba77\transformed\media-1.0.0\res
com.satya164.reactnavigationtemplate.app-viewpager2-1.0.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\8315ec2b76c6dfb54a9129d88f0598c1\transformed\viewpager2-1.0.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-service-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\852be9e6679aa434c2578b30b83f2ed6\transformed\lifecycle-service-2.6.2\res
com.satya164.reactnavigationtemplate.app-savedstate-ktx-1.2.1-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\89a14f92f7d6a0d0a0ff24a0417f205f\transformed\savedstate-ktx-1.2.1\res
com.satya164.reactnavigationtemplate.app-activity-1.8.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\8a69babe9d9651d03b9a971a79e86412\transformed\activity-1.8.0\res
com.satya164.reactnavigationtemplate.app-tracing-1.2.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\8cd6302452352550fbfa35c1ffc800a8\transformed\tracing-1.2.0\res
com.satya164.reactnavigationtemplate.app-autofill-1.1.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\93122185410ef064caadb5d5229c2b16\transformed\autofill-1.1.0\res
com.satya164.reactnavigationtemplate.app-constraintlayout-2.0.1-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\992ae729079d0d96d05b1ce2a0b0c65e\transformed\constraintlayout-2.0.1\res
com.satya164.reactnavigationtemplate.app-expo.modules.splashscreen-0.30.9-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\a271ad9d70e7fde543cccd4c3433ae14\transformed\expo.modules.splashscreen-0.30.9\res
com.satya164.reactnavigationtemplate.app-firebase-messaging-24.1.1-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\c94095c764bd93b8f5e73fc999da1958\transformed\firebase-messaging-24.1.1\res
com.satya164.reactnavigationtemplate.app-core-splashscreen-1.2.0-alpha02-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\cb430efb3b96d95de96e855fce06cf16\transformed\core-splashscreen-1.2.0-alpha02\res
com.satya164.reactnavigationtemplate.app-fragment-ktx-1.6.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\d22d246360e800147b80b90f315901c5\transformed\fragment-ktx-1.6.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-runtime-ktx-2.6.2-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\da827b85871f4bd0abe0652a3c1ed191\transformed\lifecycle-runtime-ktx-2.6.2\res
com.satya164.reactnavigationtemplate.app-savedstate-1.2.1-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\dbba9395a04335ba141d79ea360d770f\transformed\savedstate-1.2.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-livedata-2.6.2-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\e53974698227bf58e8a34890a5d6dc1b\transformed\lifecycle-livedata-2.6.2\res
com.satya164.reactnavigationtemplate.app-coordinatorlayout-1.2.0-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\e67554b16c0f66ed5dcd94f202e15e5c\transformed\coordinatorlayout-1.2.0\res
com.satya164.reactnavigationtemplate.app-drawee-3.6.0-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\f279fa17c01dbfb036d3f1125112bf06\transformed\drawee-3.6.0\res
com.satya164.reactnavigationtemplate.app-tracing-ktx-1.2.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\f3a748209e608070e671341ac3c4992a\transformed\tracing-ktx-1.2.0\res
com.satya164.reactnavigationtemplate.app-play-services-base-18.5.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\f3acfda74680b6b841c8159f43eacaed\transformed\play-services-base-18.5.0\res
com.satya164.reactnavigationtemplate.app-pngs-53 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\generated\res\pngs\debug
com.satya164.reactnavigationtemplate.app-resValues-54 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\generated\res\resValues\debug
com.satya164.reactnavigationtemplate.app-packageDebugResources-55 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.satya164.reactnavigationtemplate.app-packageDebugResources-56 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.satya164.reactnavigationtemplate.app-debug-57 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.satya164.reactnavigationtemplate.app-debug-58 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\debug\res
com.satya164.reactnavigationtemplate.app-main-59 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\res
com.satya164.reactnavigationtemplate.app-debug-60 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-61 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\auth\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-62 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\firestore\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-63 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-64 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-65 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-66 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-67 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-68 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-69 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-70 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-71 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-72 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-73 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-74 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-75 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-76 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-77 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-78 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
