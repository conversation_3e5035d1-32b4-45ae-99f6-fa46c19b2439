com.satya164.reactnavigationtemplate.app-savedstate-ktx-1.2.1-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\00f6306eeb3cb3e84a5482558c24ebe9\transformed\savedstate-ktx-1.2.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-runtime-ktx-2.6.2-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\01860643452e6005cfe44213cb825f96\transformed\lifecycle-runtime-ktx-2.6.2\res
com.satya164.reactnavigationtemplate.app-expo.modules.systemui-5.0.9-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\02a8bb41ac01e5b9d8d98e14599ed6aa\transformed\expo.modules.systemui-5.0.9\res
com.satya164.reactnavigationtemplate.app-constraintlayout-2.0.1-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0ae1ae77426d10d49824386e0ba1c6d7\transformed\constraintlayout-2.0.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-livedata-core-ktx-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\10a04639500eee96d572ce097647320e\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.satya164.reactnavigationtemplate.app-activity-1.8.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\162d1a42f11f07d53d32a7fc73b8159b\transformed\activity-1.8.0\res
com.satya164.reactnavigationtemplate.app-react-android-0.79.1-debug-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1f6d4087117984f6265a436ffccadbd8\transformed\react-android-0.79.1-debug\res
com.satya164.reactnavigationtemplate.app-media-1.0.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1fd428f32dd00854416b91f49305efd7\transformed\media-1.0.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-livedata-core-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\204340ae9317df5c2e62924d9aa458cf\transformed\lifecycle-livedata-core-2.6.2\res
com.satya164.reactnavigationtemplate.app-startup-runtime-1.1.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\224278bbfd2602c43275db4f60a5b685\transformed\startup-runtime-1.1.1\res
com.satya164.reactnavigationtemplate.app-material-1.12.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\2656d85fa34ff57ee93e0a7799c35d76\transformed\material-1.12.0\res
com.satya164.reactnavigationtemplate.app-drawerlayout-1.1.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\28cd58e0f494d6d0ff7e8819026252e4\transformed\drawerlayout-1.1.1\res
com.satya164.reactnavigationtemplate.app-autofill-1.1.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ba293eddb50ad62967051ff68141d45\transformed\autofill-1.1.0\res
com.satya164.reactnavigationtemplate.app-credentials-play-services-auth-1.2.0-rc01-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\res
com.satya164.reactnavigationtemplate.app-core-runtime-2.2.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\3a37dbe4cdb78b297aed642f0a6ff4bc\transformed\core-runtime-2.2.0\res
com.satya164.reactnavigationtemplate.app-emoji2-views-helper-1.3.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\3a58e21368425827be3dff9fb2bb64eb\transformed\emoji2-views-helper-1.3.0\res
com.satya164.reactnavigationtemplate.app-recyclerview-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\48bb3b7bd7883688b67144423083ab56\transformed\recyclerview-1.1.0\res
com.satya164.reactnavigationtemplate.app-swiperefreshlayout-1.1.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\5076e22ae1f307b1f45f29955280e0e0\transformed\swiperefreshlayout-1.1.0\res
com.satya164.reactnavigationtemplate.app-play-services-basement-18.4.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\51876ee9437d313540039b485fb1b11c\transformed\play-services-basement-18.4.0\res
com.satya164.reactnavigationtemplate.app-appcompat-1.7.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\57c3c2f9dced1e219da46eae741c7de7\transformed\appcompat-1.7.0\res
com.satya164.reactnavigationtemplate.app-fragment-ktx-1.6.1-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\59843c4e576486c9b1e775919678e5ac\transformed\fragment-ktx-1.6.1\res
com.satya164.reactnavigationtemplate.app-play-services-auth-21.3.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\res
com.satya164.reactnavigationtemplate.app-core-common-2.0.3-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\626bbc425fd1e3558c3e226edfafb624\transformed\core-common-2.0.3\res
com.satya164.reactnavigationtemplate.app-tracing-1.2.0-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\6aa8eaf1396509fe18be7d0d4ebf6f69\transformed\tracing-1.2.0\res
com.satya164.reactnavigationtemplate.app-fragment-1.6.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\703f856d78ed6d174057dda4589aa6a9\transformed\fragment-1.6.1\res
com.satya164.reactnavigationtemplate.app-transition-1.5.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\73bfb054c7e8b0963f4c6a4c980b6893\transformed\transition-1.5.0\res
com.satya164.reactnavigationtemplate.app-firebase-common-21.0.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\res
com.satya164.reactnavigationtemplate.app-savedstate-1.2.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\79f589ad2b12df599907cfa78699a91a\transformed\savedstate-1.2.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-livedata-2.6.2-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\7ebe63c9770e46a540f8dd37f205eb60\transformed\lifecycle-livedata-2.6.2\res
com.satya164.reactnavigationtemplate.app-viewpager2-1.0.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\89a25e97a4185024ee090a28b8d61845\transformed\viewpager2-1.0.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-service-2.6.2-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\96987dc59856eeb3ae99d5dd29b1d6f0\transformed\lifecycle-service-2.6.2\res
com.satya164.reactnavigationtemplate.app-credentials-1.2.0-rc01-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\a04f17c587cd6ce1f2fc9c2917a06c1d\transformed\credentials-1.2.0-rc01\res
com.satya164.reactnavigationtemplate.app-play-services-base-18.5.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\a4306a6f482b9baf272025916cdd1d72\transformed\play-services-base-18.5.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-runtime-2.6.2-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\a4ceba89600f1d5d065771259a1ed22b\transformed\lifecycle-runtime-2.6.2\res
com.satya164.reactnavigationtemplate.app-emoji2-1.3.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\res
com.satya164.reactnavigationtemplate.app-core-splashscreen-1.2.0-alpha02-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\b7dcb343aab7d0242781dd539b95240f\transformed\core-splashscreen-1.2.0-alpha02\res
com.satya164.reactnavigationtemplate.app-appcompat-resources-1.7.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\ba4d8cbc6c155999100efe3ebfb245d1\transformed\appcompat-resources-1.7.0\res
com.satya164.reactnavigationtemplate.app-profileinstaller-1.3.1-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\res
com.satya164.reactnavigationtemplate.app-cardview-1.0.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\c8c927f6912bd0e3ea2859a7c5e81971\transformed\cardview-1.0.0\res
com.satya164.reactnavigationtemplate.app-coordinatorlayout-1.2.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\cdc814363e9c04c6bf4ec283f249ace5\transformed\coordinatorlayout-1.2.0\res
com.satya164.reactnavigationtemplate.app-core-ktx-1.13.1-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\ce385334b7b38b9fcd0fe02866fd5787\transformed\core-ktx-1.13.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-viewmodel-ktx-2.6.2-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\cf48333f390e8c209fca9513a0072e87\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.satya164.reactnavigationtemplate.app-firebase-messaging-24.1.1-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\res
com.satya164.reactnavigationtemplate.app-lifecycle-viewmodel-2.6.2-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a3fd63270002a6c8aca90584da9ffa\transformed\lifecycle-viewmodel-2.6.2\res
com.satya164.reactnavigationtemplate.app-annotation-experimental-1.4.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\d8d8fa963047c56279d673eed6e32164\transformed\annotation-experimental-1.4.0\res
com.satya164.reactnavigationtemplate.app-browser-1.6.0-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\e40193e98f0e5a09a490d3ba62d94d70\transformed\browser-1.6.0\res
com.satya164.reactnavigationtemplate.app-lifecycle-viewmodel-savedstate-2.6.2-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\e629ffe154b46e9b328a5aa327e71728\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.satya164.reactnavigationtemplate.app-tracing-ktx-1.2.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\e72498b86e0196440e831c7c202bbb77\transformed\tracing-ktx-1.2.0\res
com.satya164.reactnavigationtemplate.app-expo.modules.splashscreen-0.30.9-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\ea7bdf046fc5378dfdc484f11772f1ca\transformed\expo.modules.splashscreen-0.30.9\res
com.satya164.reactnavigationtemplate.app-lifecycle-process-2.6.2-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\edfc9522cdc619166a5d89f782009dcd\transformed\lifecycle-process-2.6.2\res
com.satya164.reactnavigationtemplate.app-core-1.13.1-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\res
com.satya164.reactnavigationtemplate.app-activity-ktx-1.8.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\f6d77de12976db3f45b31114718ab679\transformed\activity-ktx-1.8.0\res
com.satya164.reactnavigationtemplate.app-drawee-3.6.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\f723693e14d3d852df649ac7b2bdad4c\transformed\drawee-3.6.0\res
com.satya164.reactnavigationtemplate.app-pngs-53 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\generated\res\pngs\debug
com.satya164.reactnavigationtemplate.app-res-54 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\generated\res\processDebugGoogleServices
com.satya164.reactnavigationtemplate.app-resValues-55 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\generated\res\resValues\debug
com.satya164.reactnavigationtemplate.app-packageDebugResources-56 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.satya164.reactnavigationtemplate.app-packageDebugResources-57 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.satya164.reactnavigationtemplate.app-debug-58 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.satya164.reactnavigationtemplate.app-debug-59 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\debug\res
com.satya164.reactnavigationtemplate.app-main-60 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\res
com.satya164.reactnavigationtemplate.app-debug-61 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-62 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\auth\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-63 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\firestore\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-64 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-65 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-66 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-67 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-68 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-69 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-70 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-71 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-72 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-73 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-74 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-75 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-76 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-77 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-78 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.satya164.reactnavigationtemplate.app-debug-79 C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
