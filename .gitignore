# ─────────────────────────────
# 📦 Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# ─────────────────────────────
# 🧪 Testing
coverage/

# ─────────────────────────────
# 🧠 TypeScript
*.tsbuildinfo

# ─────────────────────────────
# ⚙️ Build / cache / temp
dist/
build/
web-build/
.expo/
.expo-shared/
.expo-env.json
.expo-env.d.ts
.multipack/
.temp/
tmp/
temp/

# ─────────────────────────────
# 📱 Native (Android/iOS)
android/.gradle/
android/app/build/
android/build/
ios/Pods/
ios/build/
ios/*.xcodeproj/project.xcworkspace/
ios/*.xcworkspace/
*.xcuserstate

# Android keys / signing
*.jks
*.keystore
*.p8
*.p12
*.key
*.mobileprovision

# Kotlin/Swift caches
.kotlin/
.swiftpm/

# ─────────────────────────────
# 🔥 Firebase / secrets
google-services.json
GoogleService-Info.plist
firebase-debug.log

# ─────────────────────────────
# 💻 OS / IDEs
.DS_Store
Thumbs.db
.idea/
.vscode/
*.swp

# ─────────────────────────────
# 📜 Environment
.env
.env.*
!.env.example

# ─────────────────────────────
# 🗑 Misc
*.orig.*
*.pem
*.log
app-example
