{"buildFiles": ["C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\.cxx\\Debug\\1n4v2yki\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\.cxx\\Debug\\1n4v2yki\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\intermediates\\cxx\\Debug\\1n4v2yki\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\intermediates\\cxx\\Debug\\1n4v2yki\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\intermediates\\cxx\\Debug\\1n4v2yki\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\intermediates\\cxx\\Debug\\1n4v2yki\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Documents\\WebWorkspace\\proto-reactnative\\android\\app\\build\\intermediates\\cxx\\Debug\\1n4v2yki\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"artifactName": "react_codegen_RNEdgeToEdge", "abi": "arm64-v8a", "runtimeFiles": []}}}