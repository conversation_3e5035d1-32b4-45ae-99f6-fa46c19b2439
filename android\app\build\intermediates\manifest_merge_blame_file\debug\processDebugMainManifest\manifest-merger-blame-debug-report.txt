1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.satya164.reactnavigationtemplate"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:3:3-77
13-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:5:3-63
14-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:6:3-78
15-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:7-58
19-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:7-67
21-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
23-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25
26        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
26-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
26-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
27        <intent>
27-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
28            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
28-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
28-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[:react-native-firebase_auth] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
32-->[:react-native-firebase_auth] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->[:react-native-firebase_auth] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
33-->[:react-native-firebase_auth] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
34    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
34-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f441ede2cd518eab5fd95e0f0aabb5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
34-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f441ede2cd518eab5fd95e0f0aabb5\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
35-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
35-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:22-74
36    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
36-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
36-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.satya164.reactnavigationtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.satya164.reactnavigationtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:3-31:17
45        android:name="com.satya164.reactnavigationtemplate.MainApplication"
45-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:16-47
46        android:allowBackup="true"
46-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:162-188
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f320f406cab317ad471420607f539f77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:icon="@mipmap/ic_launcher"
50-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:81-115
51        android:label="@string/app_name"
51-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:48-80
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:116-161
53        android:supportsRtl="true"
53-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:221-247
54        android:theme="@style/AppTheme"
54-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:14:189-220
55        android:usesCleartextTraffic="true" >
55-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\debug\AndroidManifest.xml:6:18-53
56        <meta-data
56-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:15:5-83
57            android:name="expo.modules.updates.ENABLED"
57-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:15:16-59
58            android:value="false" />
58-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:15:60-81
59        <meta-data
59-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:16:5-105
60            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
60-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:16:16-80
61            android:value="ALWAYS" />
61-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:16:81-103
62        <meta-data
62-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:17:5-99
63            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
63-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:17:16-79
64            android:value="0" />
64-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:17:80-97
65
66        <activity
66-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:5-30:16
67            android:name="com.satya164.reactnavigationtemplate.MainActivity"
67-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:15-43
68            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
68-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:44-134
69            android:exported="true"
69-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:256-279
70            android:launchMode="singleTask"
70-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:135-166
71            android:screenOrientation="portrait"
71-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:280-316
72            android:theme="@style/Theme.App.SplashScreen"
72-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:210-255
73            android:windowSoftInputMode="adjustResize" >
73-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:18:167-209
74            <intent-filter>
74-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:19:7-22:23
75                <action android:name="android.intent.action.MAIN" />
75-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:20:9-60
75-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:20:17-58
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:21:9-68
77-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:21:19-66
78            </intent-filter>
79            <intent-filter>
79-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:23:7-29:23
80                <action android:name="android.intent.action.VIEW" />
80-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:7-58
80-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:15-56
81
82                <category android:name="android.intent.category.DEFAULT" />
82-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:9-67
82-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:19-65
83                <category android:name="android.intent.category.BROWSABLE" />
83-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:7-67
83-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:17-65
84
85                <data android:scheme="protoreactnative" />
85-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
85-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
86                <data android:scheme="exp+proto-reactnative" />
86-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
86-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
87            </intent-filter>
88        </activity>
89
90        <service
90-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
91            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
91-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
92            android:exported="false" />
92-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
93        <service
93-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
94            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
94-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
95            android:exported="false" >
95-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
96            <intent-filter>
96-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
97                <action android:name="com.google.firebase.MESSAGING_EVENT" />
97-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
97-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
98            </intent-filter>
99        </service>
100
101        <receiver
101-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
102            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
102-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
103            android:exported="true"
103-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
104            android:permission="com.google.android.c2dm.permission.SEND" >
104-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
105            <intent-filter>
105-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
106                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
106-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
106-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
107            </intent-filter>
108        </receiver>
109
110        <meta-data
110-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
111            android:name="delivery_metrics_exported_to_big_query_enabled"
111-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
112            android:value="false" />
112-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
113        <meta-data
113-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
114            android:name="firebase_messaging_auto_init_enabled"
114-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
115            android:value="true" />
115-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
116        <meta-data
116-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
117            android:name="firebase_messaging_notification_delegation_enabled"
117-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
118            android:value="false" />
118-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
119        <meta-data
119-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
120            android:name="com.google.firebase.messaging.default_notification_channel_id"
120-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
121            android:value="" />
121-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
122        <meta-data
122-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
123            android:name="com.google.firebase.messaging.default_notification_color"
123-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
124            android:resource="@color/white" />
124-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
125        <meta-data
125-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
126            android:name="app_data_collection_default_enabled"
126-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
127            android:value="true" />
127-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
128
129        <service
129-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
130            android:name="com.google.firebase.components.ComponentDiscoveryService"
130-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
131            android:directBootAware="true"
131-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
132            android:exported="false" >
132-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
133            <meta-data
133-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
134                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
134-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
136            <meta-data
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
137                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
137-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
139            <meta-data
139-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\72fe17a1aa4af3fd8eab26a167f70938\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
140                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
140-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\72fe17a1aa4af3fd8eab26a167f70938\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\72fe17a1aa4af3fd8eab26a167f70938\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
142            <meta-data
142-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\72fe17a1aa4af3fd8eab26a167f70938\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
143                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
143-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\72fe17a1aa4af3fd8eab26a167f70938\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\72fe17a1aa4af3fd8eab26a167f70938\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
145            <meta-data
145-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
146                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
146-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
148            <meta-data
148-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
149                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
149-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
151            <meta-data
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\960467e87b91661ef2168e87e0b165cd\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
152                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
152-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\960467e87b91661ef2168e87e0b165cd\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\960467e87b91661ef2168e87e0b165cd\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
154            <meta-data
154-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\960467e87b91661ef2168e87e0b165cd\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
155                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
155-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\960467e87b91661ef2168e87e0b165cd\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\960467e87b91661ef2168e87e0b165cd\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
157            <meta-data
157-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\598af0139a7a9cb4076aa2f97b6f6586\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
158                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
158-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\598af0139a7a9cb4076aa2f97b6f6586\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\598af0139a7a9cb4076aa2f97b6f6586\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
160            <meta-data
160-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
161                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
161-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
163            <meta-data
163-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8b91d00ad6194e08ad51f3e5c4297bf\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
164                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
164-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8b91d00ad6194e08ad51f3e5c4297bf\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8b91d00ad6194e08ad51f3e5c4297bf\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
166        </service>
167
168        <provider
168-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
169            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
169-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
170            android:authorities="com.satya164.reactnavigationtemplate.reactnativefirebaseappinitprovider"
170-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
171            android:exported="false"
171-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
172            android:initOrder="99" />
172-->[:react-native-firebase_app] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
173
174        <activity
174-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
175            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
175-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
176            android:exported="true"
176-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
177            android:launchMode="singleTask"
177-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
178            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
178-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
179            <intent-filter>
179-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
180                <action android:name="android.intent.action.VIEW" />
180-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:7-58
180-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:15-56
181
182                <category android:name="android.intent.category.DEFAULT" />
182-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:9-67
182-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:19-65
183                <category android:name="android.intent.category.BROWSABLE" />
183-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:7-67
183-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:17-65
184
185                <data android:scheme="expo-dev-launcher" />
185-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
185-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
186            </intent-filter>
187        </activity>
188        <activity
188-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
189            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
189-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
190            android:screenOrientation="portrait"
190-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
191            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
191-->[:expo-dev-launcher] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
192        <activity
192-->[:expo-dev-menu] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
193            android:name="expo.modules.devmenu.DevMenuActivity"
193-->[:expo-dev-menu] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
194            android:exported="true"
194-->[:expo-dev-menu] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
195            android:launchMode="singleTask"
195-->[:expo-dev-menu] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
196            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
196-->[:expo-dev-menu] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
197            <intent-filter>
197-->[:expo-dev-menu] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
198                <action android:name="android.intent.action.VIEW" />
198-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:7-58
198-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:15-56
199
200                <category android:name="android.intent.category.DEFAULT" />
200-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:9-67
200-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:19-65
201                <category android:name="android.intent.category.BROWSABLE" />
201-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:7-67
201-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:17-65
202
203                <data android:scheme="expo-dev-menu" />
203-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
203-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
204            </intent-filter>
205        </activity>
206
207        <meta-data
207-->[:expo-modules-core] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
208            android:name="org.unimodules.core.AppLoader#react-native-headless"
208-->[:expo-modules-core] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
209            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
209-->[:expo-modules-core] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
210        <meta-data
210-->[:expo-modules-core] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
211            android:name="com.facebook.soloader.enabled"
211-->[:expo-modules-core] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
212            android:value="true" />
212-->[:expo-modules-core] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
213
214        <activity
214-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f6d4087117984f6265a436ffccadbd8\transformed\react-android-0.79.1-debug\AndroidManifest.xml:19:9-21:40
215            android:name="com.facebook.react.devsupport.DevSettingsActivity"
215-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f6d4087117984f6265a436ffccadbd8\transformed\react-android-0.79.1-debug\AndroidManifest.xml:20:13-77
216            android:exported="false" />
216-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f6d4087117984f6265a436ffccadbd8\transformed\react-android-0.79.1-debug\AndroidManifest.xml:21:13-37
217
218        <provider
218-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
219            android:name="expo.modules.filesystem.FileSystemFileProvider"
219-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
220            android:authorities="com.satya164.reactnavigationtemplate.FileSystemFileProvider"
220-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
221            android:exported="false"
221-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
222            android:grantUriPermissions="true" >
222-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
223            <meta-data
223-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:70
224                android:name="android.support.FILE_PROVIDER_PATHS"
224-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-67
225                android:resource="@xml/file_system_provider_paths" />
225-->[:expo-file-system] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-67
226        </provider>
227
228        <activity
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
229            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
229-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
230            android:excludeFromRecents="true"
230-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
231            android:exported="true"
231-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
232            android:launchMode="singleTask"
232-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
233            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
233-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
234            <intent-filter>
234-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
235                <action android:name="android.intent.action.VIEW" />
235-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:7-58
235-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:15-56
236
237                <category android:name="android.intent.category.DEFAULT" />
237-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:9-67
237-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:19-65
238                <category android:name="android.intent.category.BROWSABLE" />
238-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:7-67
238-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:17-65
239
240                <data
240-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
241                    android:host="firebase.auth"
242                    android:path="/"
243                    android:scheme="genericidp" />
243-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
244            </intent-filter>
245        </activity>
246        <activity
246-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
247            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
247-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
248            android:excludeFromRecents="true"
248-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
249            android:exported="true"
249-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
250            android:launchMode="singleTask"
250-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
251            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
251-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
252            <intent-filter>
252-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c7e833d4c9cd48d8bb4ef5f8ab1bbf\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
253                <action android:name="android.intent.action.VIEW" />
253-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:7-58
253-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:9:15-56
254
255                <category android:name="android.intent.category.DEFAULT" />
255-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:9-67
255-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:25:19-65
256                <category android:name="android.intent.category.BROWSABLE" />
256-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:7-67
256-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:10:17-65
257
258                <data
258-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:7-37
259                    android:host="firebase.auth"
260                    android:path="/"
261                    android:scheme="recaptcha" />
261-->C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\android\app\src\main\AndroidManifest.xml:11:13-35
262            </intent-filter>
263        </activity>
264
265        <service
265-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
266            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
266-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
267            android:enabled="true"
267-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
268            android:exported="false" >
268-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
269            <meta-data
269-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
270                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
270-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
271                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
271-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
272        </service>
273
274        <activity
274-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
275            android:name="androidx.credentials.playservices.HiddenActivity"
275-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
276            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
276-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
277            android:enabled="true"
277-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
278            android:exported="false"
278-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
279            android:fitsSystemWindows="true"
279-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
280            android:theme="@style/Theme.Hidden" >
280-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d7c154004d9fa7f090fe9a207ab198c\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
281        </activity>
282
283        <receiver
283-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
284            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
284-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
285            android:exported="true"
285-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
286            android:permission="com.google.android.c2dm.permission.SEND" >
286-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
287            <intent-filter>
287-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
288                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
288-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
288-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
289            </intent-filter>
290
291            <meta-data
291-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
292                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
292-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
293                android:value="true" />
293-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
294        </receiver>
295        <!--
296             FirebaseMessagingService performs security checks at runtime,
297             but set to not exported to explicitly avoid allowing another app to call it.
298        -->
299        <service
299-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
300            android:name="com.google.firebase.messaging.FirebaseMessagingService"
300-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
301            android:directBootAware="true"
301-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
302            android:exported="false" >
302-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2dca38f3fbbb1f9573538d8a0e7b973\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
303            <intent-filter android:priority="-500" >
303-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
304                <action android:name="com.google.firebase.MESSAGING_EVENT" />
304-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
304-->[:react-native-firebase_messaging] C:\Users\<USER>\Documents\WebWorkspace\proto-reactnative\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
305            </intent-filter>
306        </service>
307
308        <provider
308-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
309            android:name="com.google.firebase.provider.FirebaseInitProvider"
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
310            android:authorities="com.satya164.reactnavigationtemplate.firebaseinitprovider"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
311            android:directBootAware="true"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
312            android:exported="false"
312-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
313            android:initOrder="100" />
313-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\752e6d144df45ff3b084922468464b21\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
314
315        <activity
315-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
316            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
316-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
317            android:excludeFromRecents="true"
317-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
318            android:exported="false"
318-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
319            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
319-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
320        <!--
321            Service handling Google Sign-In user revocation. For apps that do not integrate with
322            Google Sign-In, this service will never be started.
323        -->
324        <service
324-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
325            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
325-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
326            android:exported="true"
326-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
327            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
327-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
328            android:visibleToInstantApps="true" />
328-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ec4a524e729e799c6bccb7966a11a4\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
329
330        <activity
330-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4306a6f482b9baf272025916cdd1d72\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
331            android:name="com.google.android.gms.common.api.GoogleApiActivity"
331-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4306a6f482b9baf272025916cdd1d72\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
332            android:exported="false"
332-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4306a6f482b9baf272025916cdd1d72\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
333            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
333-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4306a6f482b9baf272025916cdd1d72\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
334
335        <meta-data
335-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51876ee9437d313540039b485fb1b11c\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
336            android:name="com.google.android.gms.version"
336-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51876ee9437d313540039b485fb1b11c\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
337            android:value="@integer/google_play_services_version" />
337-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51876ee9437d313540039b485fb1b11c\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
338
339        <provider
339-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
340            android:name="androidx.startup.InitializationProvider"
340-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
341            android:authorities="com.satya164.reactnavigationtemplate.androidx-startup"
341-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
342            android:exported="false" >
342-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
343            <meta-data
343-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
344                android:name="androidx.emoji2.text.EmojiCompatInitializer"
344-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
345                android:value="androidx.startup" />
345-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b51fde61db801cbd64d9048803a0f3\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
346            <meta-data
346-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfc9522cdc619166a5d89f782009dcd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
347                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
347-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfc9522cdc619166a5d89f782009dcd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
348                android:value="androidx.startup" />
348-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfc9522cdc619166a5d89f782009dcd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
349            <meta-data
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
350                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
351                android:value="androidx.startup" />
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
352        </provider>
353
354        <receiver
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
355            android:name="androidx.profileinstaller.ProfileInstallReceiver"
355-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
356            android:directBootAware="false"
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
357            android:enabled="true"
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
358            android:exported="true"
358-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
359            android:permission="android.permission.DUMP" >
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
360            <intent-filter>
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
361                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
361-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
361-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
362            </intent-filter>
363            <intent-filter>
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
364                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
364-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
364-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
365            </intent-filter>
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
367                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
370                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3773bba94166a1632750774d597269e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
371            </intent-filter>
372        </receiver>
373
374        <service
374-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc534c074e57c4eb0b6a6b8bb9cbe9ce\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
375            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
375-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc534c074e57c4eb0b6a6b8bb9cbe9ce\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
376            android:exported="false" >
376-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc534c074e57c4eb0b6a6b8bb9cbe9ce\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
377            <meta-data
377-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc534c074e57c4eb0b6a6b8bb9cbe9ce\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
378                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
378-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc534c074e57c4eb0b6a6b8bb9cbe9ce\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
379                android:value="cct" />
379-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc534c074e57c4eb0b6a6b8bb9cbe9ce\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
380        </service>
381        <service
381-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
382            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
382-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
383            android:exported="false"
383-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
384            android:permission="android.permission.BIND_JOB_SERVICE" >
384-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
385        </service>
386
387        <receiver
387-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
388            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
388-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
389            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
389-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7369117b8769bf62569bae18ec7c1487\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
390        <activity
390-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\626bbc425fd1e3558c3e226edfafb624\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
391            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
391-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\626bbc425fd1e3558c3e226edfafb624\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
392            android:exported="false"
392-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\626bbc425fd1e3558c3e226edfafb624\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
393            android:stateNotNeeded="true"
393-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\626bbc425fd1e3558c3e226edfafb624\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
394            android:theme="@style/Theme.PlayCore.Transparent" />
394-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\626bbc425fd1e3558c3e226edfafb624\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
395    </application>
396
397</manifest>
