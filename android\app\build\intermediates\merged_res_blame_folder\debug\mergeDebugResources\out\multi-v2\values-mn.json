{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-55:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7324ab0e624d5e4aa160cd354b3f9221\\transformed\\react-android-0.79.1-debug\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,135,204,285,355,421,497", "endColumns": "79,68,80,69,65,75,74", "endOffsets": "130,199,280,350,416,492,567"}, "to": {"startLines": "50,77,78,80,94,145,146", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4676,7698,7767,7907,8937,13083,13159", "endColumns": "79,68,80,69,65,75,74", "endOffsets": "4751,7762,7843,7972,8998,13154,13229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\25dd2e9a2ec645a6ceb46052888a9d56\\transformed\\appcompat-1.7.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,12757", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,12833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63e389f272c6144302a9e6a3862bfc64\\transformed\\play-services-basement-18.4.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5764", "endColumns": "149", "endOffsets": "5909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\582d0c2f5e6292ce32dc808d4c3bbb90\\transformed\\browser-1.6.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "69,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6971,7300,7404,7509", "endColumns": "104,103,104,107", "endOffsets": "7071,7399,7504,7612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\429888db8e5916503c7ec8b30991ae80\\transformed\\material-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1035,1100,1198,1279,1338,1431,1493,1556,1614,1685,1747,1801,1922,1979,2040,2094,2165,2298,2382,2462,2558,2641,2724,2857,2939,3017,3149,3239,3319,3373,3424,3490,3561,3639,3710,3789,3864,3942,4022,4105,4210,4298,4377,4467,4560,4634,4704,4795,4849,4929,4996,5080,5165,5227,5291,5354,5425,5529,5644,5741,5855,5913,5968,6052,6139,6215", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "260,339,416,495,582,667,765,884,969,1030,1095,1193,1274,1333,1426,1488,1551,1609,1680,1742,1796,1917,1974,2035,2089,2160,2293,2377,2457,2553,2636,2719,2852,2934,3012,3144,3234,3314,3368,3419,3485,3556,3634,3705,3784,3859,3937,4017,4100,4205,4293,4372,4462,4555,4629,4699,4790,4844,4924,4991,5075,5160,5222,5286,5349,5420,5524,5639,5736,5850,5908,5963,6047,6134,6210,6292"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,70,71,72,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3232,3311,3388,3467,3554,4374,4472,4591,7076,7137,7202,7617,7848,7977,8070,8132,8195,8253,8324,8386,8440,8561,8618,8679,8733,8804,9003,9087,9167,9263,9346,9429,9562,9644,9722,9854,9944,10024,10078,10129,10195,10266,10344,10415,10494,10569,10647,10727,10810,10915,11003,11082,11172,11265,11339,11409,11500,11554,11634,11701,11785,11870,11932,11996,12059,12130,12234,12349,12446,12560,12618,12673,12838,12925,13001", "endLines": "5,35,36,37,38,39,47,48,49,70,71,72,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,142,143,144", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "310,3306,3383,3462,3549,3634,4467,4586,4671,7132,7197,7295,7693,7902,8065,8127,8190,8248,8319,8381,8435,8556,8613,8674,8728,8799,8932,9082,9162,9258,9341,9424,9557,9639,9717,9849,9939,10019,10073,10124,10190,10261,10339,10410,10489,10564,10642,10722,10805,10910,10998,11077,11167,11260,11334,11404,11495,11549,11629,11696,11780,11865,11927,11991,12054,12125,12229,12344,12441,12555,12613,12668,12752,12920,12996,13078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2763bc7439cd222ae35e1433f4611677\\transformed\\credentials-1.2.0-rc01\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,113", "endOffsets": "161,275"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3007,3118", "endColumns": "110,113", "endOffsets": "3113,3227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\480ce62020c830cf93653ad6a17bbf11\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "40,41,42,43,44,45,46,147", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3639,3737,3839,3940,4038,4143,4255,13234", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3732,3834,3935,4033,4138,4250,4369,13330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3acfda74680b6b841c8159f43eacaed\\transformed\\play-services-base-18.5.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4756,4866,5023,5155,5262,5399,5525,5654,5914,6058,6165,6333,6462,6603,6771,6832,6894", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "4861,5018,5150,5257,5394,5520,5649,5759,6053,6160,6328,6457,6598,6766,6827,6889,6966"}}]}]}