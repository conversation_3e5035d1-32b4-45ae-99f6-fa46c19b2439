# ninja log v5
1	15	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a/CMakeFiles/cmake.verify_globs	60345c74ab092a3b
61	4097	7724710920849128	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	f55333dbccca8cdc
21	2945	7724710909127546	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	55ccb8d2449e43f3
27	3269	7724710912576707	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	95a03b73ca0cecba
104	4454	7724710924649731	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	a78914537e42a801
53	3065	7724710910298413	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	3ce80dfdae64db53
41	3364	7724710913459467	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	fa2f208a4f677ee1
89	3206	7724710911887942	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/States.cpp.o	baf10bac46679321
70	3561	7724710915193934	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	90a5412d6258d284
30	3884	7724710918508862	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	50708da2c28d2d96
3065	6782	7724710947928077	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/Props.cpp.o	bb8a99cc86894fd3
18	4178	7724710921571661	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	51f623a623b19251
49	4165	7724710921694326	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	1e5c21a9afa347a5
93	4190	7724710921977895	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	ffa1b1bc684feedb
57	3885	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	247e8bcde9b6f916
24	3725	7724710917205249	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	f4b9fd4055814848
139	5049	7724710930594314	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderConfigShadowNode.cpp.o	73e6a782ed642699
3269	6060	7724710940796210	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/61c24ba14db9fe7ad90891edb7913c7d/renderer/components/rnscreens/RNSScreenState.cpp.o	50d50b2f58f9cff0
152	4976	7724710929937900	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/ShadowNodes.cpp.o	cf9e89310864912c
120	4055	7724710920460754	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	c2cf50ec651eb7e1
146	4231	7724710922369167	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	ac21b8829718d21e
34	4324	7724710923400342	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	48bef8a675ab8b08
9	4724	7724710927330044	CMakeFiles/appmodules.dir/OnLoad.cpp.o	cfb8e89ebc316ba9
8937	9045	7724710970539073	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/arm64-v8a/libappmodules.so	3d938506a235b0b5
15	4215	7724710922251115	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	21571c04e71e3350
84	4624	7724710926394725	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewState.cpp.o	c528153ffc6c472e
99	4921	7724710929337006	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	369e591f37ce4e5f
3562	6048	7724710940670813	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	42a80d633cc12c98
126	4891	7724710929074838	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	d4e8980d05656b9
115	5045	7724710930614331	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSModalScreenShadowNode.cpp.o	d48b3adb8e111e0a
110	5129	7724710931385847	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderSubviewShadowNode.cpp.o	57435914ba30c73d
133	5173	7724710931832221	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	d42065cdeec91249
74	4918	7724710929260402	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/Props.cpp.o	e843510112fe58ed
3207	5592	7724710936118600	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/States.cpp.o	89eb4de188b2ee7b
37	5226	7724710932334741	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	e3bc5877f15d0091
65	5476	7724710934908844	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	be9879cf3d154fc1
79	5575	7724710935856796	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/ComponentDescriptors.cpp.o	9b9d28a89af31a5d
5576	5675	7724710936888167	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/arm64-v8a/libreact_codegen_safeareacontext.so	6bab9ecbf950d2ce
45	5663	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	d2a404379d64b08
158	5193	7724710932096832	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	5e76d8180b901e95
3364	7252	7724710952592733	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	d9c2536bdb4e754b
2945	7913	7724710959106629	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	947703747670eb6
7913	8006	7724710960138930	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/arm64-v8a/libreact_codegen_rnscreens.so	4ce20e9994273a13
12	8937	7724710969095414	CMakeFiles/appmodules.dir/79b7bec8534d0488d02d2214d6959394/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	8c41ca1e72e8df4
2	30	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/arm64-v8a/CMakeFiles/cmake.verify_globs	60345c74ab092a3b
163	5251	7724718258420700	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/States.cpp.o	f6620d9d67e95659
39	5288	7724718258829373	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	a369dcb6c24c8b0
98	5312	7724718259037710	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	8fe911961cacb27
68	5473	7724718260777224	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	79e47e4de9d030f1
44	5506	7724718261075002	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	343de3e57a9263dc
123	5746	7724718263181271	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	ffc607fb5f10e59b
155	5842	7724718263948503	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	9b720bb551e85cd2
92	5843	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	803a835e24cc8b4f
34	5907	7724718264979789	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	e34f92ffbbf9d6dc
62	5926	7724718265057982	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	1eb228b9fe13c89e
169	5928	7724718265005118	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	3ea41c51ea466db0
104	6036	7724718266278291	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	aaa1f124a476138a
50	6038	7724718266308583	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	d64bcb5de1042f11
183	6185	7724718267764716	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	fc4f856f45eb8d5f
218	6275	7724718268737842	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	614c0483db7854eb
56	6580	7724718271818838	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	b9f2f1b062350c51
86	6590	7724718271859551	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	636bcefecbde52e3
22	6683	7724718272758246	CMakeFiles/appmodules.dir/OnLoad.cpp.o	b9401dddddd9fe16
28	6749	7724718273500945	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	f0dcb16bb4976199
147	6854	7724718274567925	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	9ba83dcdaa70a7e2
116	6909	7724718275065115	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewState.cpp.o	65fffaf73d4feb07
191	6948	7724718275475296	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	4efb8f482e3d3ff1
204	7065	7724718276647004	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderSubviewShadowNode.cpp.o	86bf224028b9c0b7
211	7068	7724718276627053	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSModalScreenShadowNode.cpp.o	fe7baffb5889dacb
197	7110	7724718277051102	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderConfigShadowNode.cpp.o	bd4bccf65b131c1c
129	7173	7724718277649487	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ffca5788ae2511e958786a570a961d13/components/safeareacontext/Props.cpp.o	4f777be507aa421e
176	7192	7724718277886991	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	bf9a3372406810d7
110	7608	7724718282081041	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	2072d103141651f3
81	7625	7724718282138343	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	2baabdb9301054be
5506	7737	7724718283419725	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/States.cpp.o	91f251b00e06024
137	7788	7724718283828514	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/ComponentDescriptors.cpp.o	9505756cd4d13d9b
74	7796	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	9d96db89cd951419
232	7879	7724718284683402	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db06d6068da9a843e25959b212b84c4c/jni/react/renderer/components/rnscreens/Props.cpp.o	e204a359491bc673
7789	8008	7724718286060211	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/arm64-v8a/libreact_codegen_safeareacontext.so	50cfdd561fe9f0da
5288	8073	7724718286788772	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/61c24ba14db9fe7ad90891edb7913c7d/renderer/components/rnscreens/RNSScreenState.cpp.o	77abf55acfa7a0f3
5746	8303	7724718289070573	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	b96be4661265363c
5313	8473	7724718290804476	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/ShadowNodes.cpp.o	20f3f5e623ce2cd
5474	8773	7724718293805292	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	d9002a0d2fb95fbc
225	9030	7724718296071396	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	14774126389cbba3
5252	9261	7724718298554165	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	a3278ce493671ae4
9261	9365	7724718299600271	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/arm64-v8a/libreact_codegen_rnscreens.so	1c6f9efae792d377
17	11025	7724718315855383	CMakeFiles/appmodules.dir/79b7bec8534d0488d02d2214d6959394/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	21252055ffe9e463
11025	11148	7724718317432906	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/arm64-v8a/libappmodules.so	50dcb9b6b366fbb6
