import { FIREBASE_AUTH, FIREBASE_DB, FIREBASE_MESSAGING } from '../../FirebaseConfig';
import { testFCMSetup } from './FCMTest';

/**
 * Test Firebase initialization
 * Call this function to verify Firebase is properly set up
 */
export const testFirebaseInitialization = async () => {
  try {
    console.log('🔥 Testing Firebase initialization...');

    // Test Auth
    console.log('📧 Firebase Auth:', FIREBASE_AUTH ? '✅ Initialized' : '❌ Failed');

    // Test Firestore
    console.log('🗄️ Firebase Firestore:', FIREBASE_DB ? '✅ Initialized' : '❌ Failed');

    // Test Messaging
    console.log('📱 Firebase Messaging:', FIREBASE_MESSAGING ? '✅ Initialized' : '❌ Failed');

    // Test Auth state
    const currentUser = FIREBASE_AUTH.currentUser;
    console.log(
      '👤 Current User:',
      currentUser ? `✅ ${currentUser.email}` : '❌ Not authenticated',
    );

    // Test app instance
    console.log(
      '🏗️ Firebase App:',
      'Default app should be auto-initialized from google-services.json',
    );

    console.log('🎉 Firebase test completed!');

    // Also test FCM setup
    await testFCMSetup();

    return true;
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    console.error('💡 Make sure google-services.json is in android/app/ directory');
    return false;
  }
};

/**
 * Test Firebase connection with a simple Firestore read
 */
export const testFirestoreConnection = async () => {
  try {
    console.log('🔍 Testing Firestore connection...');

    // Try to read from a test collection
    const testDoc = await FIREBASE_DB.collection('test').doc('connection').get();
    console.log('📄 Firestore connection:', testDoc ? '✅ Connected' : '❌ Failed');

    return true;
  } catch (error) {
    console.error('❌ Firestore connection failed:', error);
    return false;
  }
};
