import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useEffect, useState } from 'react';
import { FIREBASE_AUTH } from '../FirebaseConfig';
import { Login } from './navigation/screens/Login';
import { Navigation } from './navigation';
import { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { useNotifications } from './Notifications/useNotifications';
import { testFirebaseInitialization } from './utils/FirebaseTest';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

const AuthStack = createNativeStackNavigator();

export function App() {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);

  useNotifications();

  useEffect(() => {
    // Test Firebase initialization on app start
    testFirebaseInitialization();

    const unsubscribe = FIREBASE_AUTH.onAuthStateChanged((user) => {
      setUser(user);
    });

    return unsubscribe;
  }, []);

  if (user) {
    return (
      <GestureHandlerRootView style={{ flex: 1 }}>
        <Navigation />
      </GestureHandlerRootView>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <NavigationContainer>
        <AuthStack.Navigator>
          <AuthStack.Screen name="Login" component={Login} options={{ headerShown: false }} />
        </AuthStack.Navigator>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}
