import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useEffect, useState } from 'react';
import { FIREBASE_AUTH, FIREBASE_MESSAGING } from '../FirebaseConfig';
import { Login } from './navigation/screens/Login';
import { Navigation } from './navigation';
import { AppRegistry } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { useNotifications } from './Notifications/useNotifications';
import { testFirebaseInitialization } from './utils/FirebaseTest';

const AuthStack = createNativeStackNavigator();

// Register background handler
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
  console.log('Message handled in the background!', remoteMessage);
});

export function App() {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);

  useNotifications();

  useEffect(() => {
    // Test Firebase initialization on app start
    testFirebaseInitialization();

    const unsubscribe = FIREBASE_AUTH.onAuthStateChanged((user) => {
      setUser(user);
    });

    // FCM: Ask permission + get token
    FIREBASE_MESSAGING.requestPermission().then((authStatus) => {
      console.log('FCM permission status:', authStatus);
    });

    return unsubscribe;
  }, []);

  if (user) return <Navigation />;

  return (
    <NavigationContainer>
      <AuthStack.Navigator>
        <AuthStack.Screen name="Login" component={Login} options={{ headerShown: false }} />
      </AuthStack.Navigator>
    </NavigationContainer>
  );
}

AppRegistry.registerComponent('proto-reactnative', () => App);
