# ninja log v5
61	3818	7724704205886969	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	9460d7d8d270a1f2
31	2494	7724704192605973	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	3b7a66e169836214
97	4047	7724704208264457	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	8093e8e8e87032e5
1	17	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/armeabi-v7a/CMakeFiles/cmake.verify_globs	bb6d166975162541
19	3085	7724704198648481	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	b721bbf078bb6c57
149	3682	7724704204157328	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenState.cpp.o	84070decd34e28f9
46	2747	7724704194774743	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	db974c1a44dbdfeb
39	2971	7724704197403111	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	4a708d3aedfc835e
86	2768	7724704195070155	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/States.cpp.o	dd7d85d5f3e3e54c
69	3224	7724704199385194	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	29260621d424705e
22	3509	7724704202695665	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	9877514a84a303aa
137	4627	7724704214084440	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderConfigShadowNode.cpp.o	a1da219a80f50c5a
143	3883	7724704206597181	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	b09cda1081514bde
25	3524	7724704202815728	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	c0f999d93e6c9d06
17	3608	7724704203751856	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	11ddcfdc99a73ee4
53	3898	7724704206832403	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	def43870d21fc97f
10	1481	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	fb58d8d5138372aa
131	3815	7724704205962085	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	1ab1f4d7c9634fe2
92	3680	7724704204007192	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	c20bd2ee86c234a4
103	4456	7724704212306869	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSModalScreenShadowNode.cpp.o	267cebedfc7f8ffe
7525	7626	7724704244048572	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/armeabi-v7a/libreact_codegen_rnscreens.so	7fa5bfaea9d7d99f
14	3881	7724704206597181	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	9344da9e43d1c9de
28	3862	7724704206471704	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	28d2299c04745e4b
73	4081	7724704208624827	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewState.cpp.o	4077c82f9c5b3e5a
8	4241	7724704210217090	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ac2c6cf466ec17bc
77	4402	7724704211854078	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	95fe28d2b85d761c
3225	5695	7724704224816687	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	2480b50afa903144
108	4287	7724704210722663	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	e1e5ed3df2789607
82	4561	7724704213378787	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/Props.cpp.o	bc1429d2912f7a8c
114	4584	7724704213643887	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	bc5b3a09dc9955e7
125	4599	7724704213829055	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderSubviewShadowNode.cpp.o	23341dcc6f6afb4d
35	4758	7724704215315750	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	8b82f6b8d917cab1
57	4877	7724704216521703	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	5cee03b6028ee6c2
2747	6436	7724704232132667	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/Props.cpp.o	bc3af18b7b640b97
3085	5186	7724704219721952	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/States.cpp.o	a291ecc20901af79
7	2281	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	1e5314ce055b23f8
65	5220	7724704219957290	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/ComponentDescriptors.cpp.o	9b8fb514af19741b
5220	5351	7724704221338718	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	898bc88738b0cb89
2768	5888	7724704226778385	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/ShadowNodes.cpp.o	ab4d9a91d6e14b9e
119	4804	7724704215896385	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	aaa6c3a709dcfea8
2495	7525	7724704242922315	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	d1dae439fcc9d5e6
2971	6806	7724704235810059	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	eaf2e6fe28c0cc92
11	8683	7724704254213146	CMakeFiles/appmodules.dir/79b7bec8534d0488d02d2214d6959394/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	4d2c5f39c033f540
2281	2406	7724705889151978	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/armeabi-v7a/libappmodules.so	53215e8bb6f480d1
1	17	0	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/.cxx/Debug/1n4v2yki/armeabi-v7a/CMakeFiles/cmake.verify_globs	bb6d166975162541
34	3083	7724707172347729	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	3b7a66e169836214
47	3238	7724707173484366	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	db974c1a44dbdfeb
90	3471	7724707175697379	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/States.cpp.o	dd7d85d5f3e3e54c
20	3671	7724707178118357	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	b721bbf078bb6c57
43	3874	7724707179914253	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	4a708d3aedfc835e
23	4105	7724707182482271	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	9877514a84a303aa
51	4255	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	fb58d8d5138372aa
26	4299	7724707184501388	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	c0f999d93e6c9d06
84	4418	7724707185127286	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/EventEmitters.cpp.o	29260621d424705e
63	4539	7724707186297807	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	9460d7d8d270a1f2
136	4544	7724707186795685	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenState.cpp.o	84070decd34e28f9
113	4643	7724707187827791	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06b22b1b30f144089ac051da8bb67ffe/jni/safeareacontext-generated.cpp.o	8093e8e8e87032e5
55	4647	7724707187942883	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	def43870d21fc97f
14	4672	7724707188002913	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	11ddcfdc99a73ee4
143	4697	7724707188568953	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	b09cda1081514bde
101	4752	7724707188924672	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/safeareacontextJSI-generated.cpp.o	c20bd2ee86c234a4
30	4765	7724707189165012	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	28d2299c04745e4b
17	4779	7724707189465494	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	9344da9e43d1c9de
156	4847	7724707190036536	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	1ab1f4d7c9634fe2
78	5127	7724707192986956	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewState.cpp.o	4077c82f9c5b3e5a
11	5225	7724707193923221	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ac2c6cf466ec17bc
95	5239	7724707193993238	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/Props.cpp.o	bc1429d2912f7a8c
119	5402	7724707195820635	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	e1e5ed3df2789607
107	5464	7724707196421954	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc06243f85d8f6463ad2e4fbe181fc68/safeareacontext/ShadowNodes.cpp.o	95fe28d2b85d761c
163	5524	7724707196977527	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderSubviewShadowNode.cpp.o	23341dcc6f6afb4d
59	5546	7724707197032558	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	8b82f6b8d917cab1
125	5571	7724707197418165	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f81dfcddb790e34a72eaa5b61c0bf91a/rnscreens/RNSModalScreenShadowNode.cpp.o	267cebedfc7f8ffe
130	5574	7724707197428163	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7805c2643d52f64fd1db6e5aaee5bf3/components/rnscreens/RNSScreenShadowNode.cpp.o	bc5b3a09dc9955e7
169	5750	7724707199245540	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d3909333b9fa80344619c6ba5e10a007/RNSScreenStackHeaderConfigShadowNode.cpp.o	a1da219a80f50c5a
150	5877	7724707200572511	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	aaa6c3a709dcfea8
68	6000	7724707201734380	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b71bb0bb8449bf5cb873f344c900de/RNCSafeAreaViewShadowNode.cpp.o	5cee03b6028ee6c2
38	6011	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	1e5314ce055b23f8
3239	6126	7724707203105287	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/States.cpp.o	a291ecc20901af79
74	6230	7724707203923756	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/821b3ceb3b53da2645150b406a392297/ComponentDescriptors.cpp.o	9b8fb514af19741b
6230	6348	7724707205235382	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	898bc88738b0cb89
3472	6648	7724707208273836	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/rnscreensJSI-generated.cpp.o	2480b50afa903144
4106	7199	7724707213826826	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/ShadowNodes.cpp.o	ab4d9a91d6e14b9e
3671	7566	7724707217361515	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e33a6dc7c1e01eb8f675429f3b256269/react/renderer/components/rnscreens/Props.cpp.o	bc3af18b7b640b97
3875	7950	7724707221194761	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6f87e64fc07b28e40596b577dcff19a5/renderer/components/rnscreens/EventEmitters.cpp.o	eaf2e6fe28c0cc92
3083	8664	7724707228230988	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74aa438f27a0f3eb8d9862433e72106f/components/rnscreens/ComponentDescriptors.cpp.o	d1dae439fcc9d5e6
8664	8757	7724707229266598	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/armeabi-v7a/libreact_codegen_rnscreens.so	7fa5bfaea9d7d99f
8	9676	7724707238116775	CMakeFiles/appmodules.dir/79b7bec8534d0488d02d2214d6959394/proto-reactnative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	4d2c5f39c033f540
9677	9791	7724707239622480	C:/Users/<USER>/Documents/WebWorkspace/proto-reactnative/android/app/build/intermediates/cxx/Debug/1n4v2yki/obj/armeabi-v7a/libappmodules.so	53215e8bb6f480d1
