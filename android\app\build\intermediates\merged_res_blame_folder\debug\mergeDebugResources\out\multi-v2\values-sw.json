{"logs": [{"outputFile": "com.satya164.reactnavigationtemplate.app-mergeDebugResources-56:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e40193e98f0e5a09a490d3ba62d94d70\\transformed\\browser-1.6.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7098,7437,7538,7655", "endColumns": "113,100,116,102", "endOffsets": "7207,7533,7650,7753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f320f406cab317ad471420607f539f77\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "41,42,43,44,45,46,47,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3717,3811,3913,4010,4111,4218,4325,14539", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3806,3908,4005,4106,4213,4320,4435,14635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1f6d4087117984f6265a436ffccadbd8\\transformed\\react-android-0.79.1-debug\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,350,433,502,570,649,734,817,900,972,1062,1152,1231,1314,1398,1480,1556,1632,1719,1794,1877,1952", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "119,205,276,345,428,497,565,644,729,812,895,967,1057,1147,1226,1309,1393,1475,1551,1627,1714,1789,1872,1947,2025"}, "to": {"startLines": "33,51,77,79,80,82,96,97,98,145,146,147,148,153,154,155,156,157,158,159,160,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3005,4751,7758,7902,7971,8117,9164,9232,9311,13225,13308,13391,13463,13882,13972,14051,14134,14218,14300,14376,14452,14640,14715,14798,14873", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "3069,4832,7824,7966,8049,8181,9227,9306,9391,13303,13386,13458,13548,13967,14046,14129,14213,14295,14371,14447,14534,14710,14793,14868,14946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2656d85fa34ff57ee93e0a7799c35d76\\transformed\\material-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1044,1112,1204,1277,1340,1426,1488,1551,1616,1684,1747,1801,1933,1990,2052,2106,2180,2318,2399,2479,2582,2666,2746,2878,2963,3050,3191,3279,3358,3412,3465,3531,3603,3685,3756,3841,3913,3988,4059,4132,4238,4335,4409,4504,4601,4675,4760,4860,4913,4998,5066,5154,5244,5306,5370,5433,5500,5617,5729,5840,5951,6009,6066,6147,6232,6313", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "254,330,404,477,574,663,762,891,974,1039,1107,1199,1272,1335,1421,1483,1546,1611,1679,1742,1796,1928,1985,2047,2101,2175,2313,2394,2474,2577,2661,2741,2873,2958,3045,3186,3274,3353,3407,3460,3526,3598,3680,3751,3836,3908,3983,4054,4127,4233,4330,4404,4499,4596,4670,4755,4855,4908,4993,5061,5149,5239,5301,5365,5428,5495,5612,5724,5835,5946,6004,6061,6142,6227,6308,6388"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3308,3384,3458,3531,3628,4440,4539,4668,7212,7277,7345,7829,8054,8186,8272,8334,8397,8462,8530,8593,8647,8779,8836,8898,8952,9026,9396,9477,9557,9660,9744,9824,9956,10041,10128,10269,10357,10436,10490,10543,10609,10681,10763,10834,10919,10991,11066,11137,11210,11316,11413,11487,11582,11679,11753,11838,11938,11991,12076,12144,12232,12322,12384,12448,12511,12578,12695,12807,12918,13029,13087,13144,13636,13721,13802", "endLines": "5,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "304,3379,3453,3526,3623,3712,4534,4663,4746,7272,7340,7432,7897,8112,8267,8329,8392,8457,8525,8588,8642,8774,8831,8893,8947,9021,9159,9472,9552,9655,9739,9819,9951,10036,10123,10264,10352,10431,10485,10538,10604,10676,10758,10829,10914,10986,11061,11132,11205,11311,11408,11482,11577,11674,11748,11833,11933,11986,12071,12139,12227,12317,12379,12443,12506,12573,12690,12802,12913,13024,13082,13139,13220,13716,13797,13877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57c3c2f9dced1e219da46eae741c7de7\\transformed\\appcompat-1.7.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,13553", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,13631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a4306a6f482b9baf272025916cdd1d72\\transformed\\play-services-base-18.5.0\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4837,4941,5097,5222,5331,5494,5619,5738,5998,6163,6269,6423,6550,6700,6859,6927,7002", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "4936,5092,5217,5326,5489,5614,5733,5847,6158,6264,6418,6545,6695,6854,6922,6997,7093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51876ee9437d313540039b485fb1b11c\\transformed\\play-services-basement-18.4.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5852", "endColumns": "145", "endOffsets": "5993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a04f17c587cd6ce1f2fc9c2917a06c1d\\transformed\\credentials-1.2.0-rc01\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3074,3186", "endColumns": "111,121", "endOffsets": "3181,3303"}}]}]}