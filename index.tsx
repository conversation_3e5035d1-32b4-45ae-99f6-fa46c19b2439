import './gesture-handler';

import '@expo/metro-runtime'; // Necessary for Fast Refresh on Web
import { AppRegistry } from 'react-native';
import messaging from '@react-native-firebase/messaging';

import { App } from './src/App';

// Register background handler BEFORE app registration
// This is critical for FCM to work properly
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
  console.log('🔔 Background message received:', remoteMessage);
  console.log('📱 Message data:', remoteMessage.data);
  console.log('📝 Message notification:', remoteMessage.notification);

  // You can perform background tasks here
  // Note: Keep this function lightweight and fast
});

// Register the main app component
AppRegistry.registerComponent('main', () => App);
